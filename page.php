<?php
/**
 * Page template
 * 
 * @package UpCode
 * @since 1.0.0
 */

get_header(); ?>

<body <?php body_class(); ?>>
    <div class="container" role="main">
        <?php get_sidebar(); ?>
        <main class="main" aria-label="<?php esc_attr_e('Page content area', 'upcode'); ?>">
            <?php get_template_part('top-bar'); ?>
            
            <?php if (have_posts()) : ?>
                <?php while (have_posts()) : the_post(); ?>
                    <article id="page-<?php the_ID(); ?>" <?php post_class('page-content'); ?>>
                        <header class="page-header">
                            <h1 class="page-title"><?php the_title(); ?></h1>
                            
                            <?php if (get_the_excerpt()) : ?>
                                <div class="page-excerpt">
                                    <?php the_excerpt(); ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="page-meta">
                                <div class="page-info">
                                    <span class="page-date">
                                        <?php esc_html_e('Last updated:', 'upcode'); ?>
                                        <time datetime="<?php echo esc_attr(get_the_modified_date('c')); ?>">
                                            <?php echo get_the_modified_date(); ?>
                                        </time>
                                    </span>
                                    <?php if (get_the_author()) : ?>
                                        <span class="page-author">
                                            <?php esc_html_e('By', 'upcode'); ?>
                                            <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                                <?php the_author(); ?>
                                            </a>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="page-featured-image">
                                    <?php the_post_thumbnail('large', array('class' => 'featured-image')); ?>
                                </div>
                            <?php endif; ?>
                        </header>
                        
                        <div class="page-content-body">
                            <?php the_content(); ?>
                            
                            <?php
                            wp_link_pages(array(
                                'before' => '<div class="page-links">' . esc_html__('Pages:', 'upcode'),
                                'after'  => '</div>',
                                'link_before' => '<span class="page-number">',
                                'link_after' => '</span>',
                            ));
                            ?>
                        </div>
                        
                        <footer class="page-footer">
                            <?php
                            // Show edit link for logged in users
                            if (is_user_logged_in()) :
                                edit_post_link(
                                    sprintf(
                                        wp_kses(
                                            __('Edit <span class="screen-reader-text">%s</span>', 'upcode'),
                                            array(
                                                'span' => array(
                                                    'class' => array(),
                                                ),
                                            )
                                        ),
                                        get_the_title()
                                    ),
                                    '<div class="edit-link">',
                                    '</div>'
                                );
                            endif;
                            ?>
                        </footer>
                    </article>
                    
                    <?php
                    // If comments are open or we have at least one comment, load up the comment template.
                    if (comments_open() || get_comments_number()) :
                        comments_template();
                    endif;
                    ?>
                    
                <?php endwhile; ?>
                
            <?php else : ?>
                <div class="no-page-found">
                    <div class="no-page-icon">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h2><?php esc_html_e('Page not found', 'upcode'); ?></h2>
                    <p><?php esc_html_e('Sorry, the page you are looking for could not be found.', 'upcode'); ?></p>
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="btn-home">
                        <?php esc_html_e('Back to Home', 'upcode'); ?>
                    </a>
                </div>
            <?php endif; ?>
            
            <!-- Related Pages (if any) -->
            <?php
            // Get child pages if this is a parent page
            $child_pages = get_pages(array(
                'parent' => get_the_ID(),
                'sort_column' => 'menu_order',
                'sort_order' => 'ASC'
            ));
            
            if ($child_pages && have_posts()) :
            ?>
                <section class="child-pages" aria-label="<?php esc_attr_e('Related pages', 'upcode'); ?>">
                    <h3><?php esc_html_e('Related Pages', 'upcode'); ?></h3>
                    <div class="child-pages-grid">
                        <?php foreach ($child_pages as $child_page) : ?>
                            <article class="child-page-card">
                                <?php if (has_post_thumbnail($child_page->ID)) : ?>
                                    <div class="child-page-image">
                                        <a href="<?php echo esc_url(get_permalink($child_page->ID)); ?>">
                                            <?php echo get_the_post_thumbnail($child_page->ID, 'upcode-card', array('alt' => $child_page->post_title)); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="child-page-content">
                                    <h4 class="child-page-title">
                                        <a href="<?php echo esc_url(get_permalink($child_page->ID)); ?>">
                                            <?php echo esc_html($child_page->post_title); ?>
                                        </a>
                                    </h4>
                                    
                                    <?php if ($child_page->post_excerpt) : ?>
                                        <p class="child-page-excerpt">
                                            <?php echo esc_html($child_page->post_excerpt); ?>
                                        </p>
                                    <?php else : ?>
                                        <p class="child-page-excerpt">
                                            <?php echo wp_trim_words($child_page->post_content, 15, '...'); ?>
                                        </p>
                                    <?php endif; ?>
                                    
                                    <div class="child-page-meta">
                                        <span class="child-page-date">
                                            <?php echo get_the_modified_date('', $child_page->ID); ?>
                                        </span>
                                    </div>
                                </div>
                            </article>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
            
            <!-- Parent Page Navigation (if this is a child page) -->
            <?php
            $parent_id = wp_get_post_parent_id(get_the_ID());
            if ($parent_id) :
                $parent_page = get_post($parent_id);
                $sibling_pages = get_pages(array(
                    'parent' => $parent_id,
                    'sort_column' => 'menu_order',
                    'sort_order' => 'ASC',
                    'exclude' => get_the_ID()
                ));
            ?>
                <nav class="page-navigation" aria-label="<?php esc_attr_e('Page navigation', 'upcode'); ?>">
                    <div class="parent-page">
                        <h4><?php esc_html_e('Parent Page', 'upcode'); ?></h4>
                        <a href="<?php echo esc_url(get_permalink($parent_id)); ?>" class="parent-page-link">
                            ← <?php echo esc_html($parent_page->post_title); ?>
                        </a>
                    </div>
                    
                    <?php if ($sibling_pages) : ?>
                        <div class="sibling-pages">
                            <h4><?php esc_html_e('Other Pages', 'upcode'); ?></h4>
                            <ul class="sibling-pages-list">
                                <?php foreach ($sibling_pages as $sibling) : ?>
                                    <li>
                                        <a href="<?php echo esc_url(get_permalink($sibling->ID)); ?>">
                                            <?php echo esc_html($sibling->post_title); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </nav>
            <?php endif; ?>
        </main>
    </div>
    
<?php get_footer(); ?>
