<?php
/**
 * Page template
 *
 * @package UpCode
 * @since 1.0.0
 */

get_header(); ?>

<body <?php body_class(); ?>>
    <div class="container" role="main">
        <?php get_sidebar(); ?>
        <main class="main" aria-label="<?php esc_attr_e('Page content area', 'upcode'); ?>">
            <?php get_template_part('top-bar'); ?>

            <?php if (have_posts()) : ?>
                <?php while (have_posts()) : the_post(); ?>
                    <article id="page-<?php the_ID(); ?>" <?php post_class('page-content'); ?>>
                        <header class="page-header">
                            <h1 class="page-title"><?php the_title(); ?></h1>

                            <?php if (has_post_thumbnail()) : ?>
                                <div class="page-featured-image">
                                    <?php the_post_thumbnail('large', array('class' => 'featured-image')); ?>
                                </div>
                            <?php endif; ?>
                        </header>

                        <div class="page-content-body">
                            <?php the_content(); ?>
                        </div>

                        <?php if (is_user_logged_in()) : ?>
                            <footer class="page-footer">
                                <?php edit_post_link(__('Edit Page', 'upcode'), '<div class="edit-link">', '</div>'); ?>
                            </footer>
                        <?php endif; ?>
                    </article>

                    <?php
                    // If comments are open or we have at least one comment, load up the comment template.
                    if (comments_open() || get_comments_number()) :
                        comments_template();
                    endif;
                    ?>

                <?php endwhile; ?>

            <?php else : ?>
                <div class="no-page-found">
                    <h2><?php esc_html_e('Page not found', 'upcode'); ?></h2>
                    <p><?php esc_html_e('Sorry, the page you are looking for could not be found.', 'upcode'); ?></p>
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="btn-home">
                        <?php esc_html_e('Back to Home', 'upcode'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </main>
    </div>
    
<?php get_footer(); ?>
