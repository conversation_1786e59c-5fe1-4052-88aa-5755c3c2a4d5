/* Reset & base */
* {
  box-sizing: border-box;
}

/* CSS Variables for theming */
:root {
  --bg-primary: #121212;
  --bg-secondary: #181818;
  --bg-tertiary: #1e1e1e;
  --text-primary: #e0e0e0;
  --text-secondary: #bfbfbf;
  --text-accent: #ff3e45;
  --border-color: #2c2c2c;
  --card-bg: #1a1a1a;
  --sidebar-bg: #181818;
}

/* Light theme variables */
.light-theme {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #ffffff;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-accent: #ff3e45;
  --border-color: #dee2e6;
  --card-bg: #ffffff;
  --sidebar-bg: #f8f9fa;
}

body {
  margin: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  transition: background-color 0.3s ease, color 0.3s ease;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

img {
  display: block;
  max-width: 100%;
  height: auto;
  border-radius: 6px;
}

/* Scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-thumb {
  background-color: #555;
  border-radius: 3px;
}

/* Layout grid */
.container {
  display: grid;
  grid-template-columns: 280px 1fr;
  height: 100vh;
  overflow: hidden;
  gap: 0;
  width: 100%;
  max-width: 100vw;
}

.sidebar {
  background-color: var(--sidebar-bg);
  padding: 28px 24px 32px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  border-right: 1px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  z-index: 100;
}

.main {
  background-color: var(--bg-primary);
  padding: 32px 40px 48px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s ease;
  position: relative;
  max-width: 100%;
}

/* Sidebar content */
.logo {
  font-weight: 800;
  font-size: 26px;
  color: var(--text-primary);
  margin-bottom: 24px;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 10px;
  user-select: none;
  transition: color 0.3s ease;
}

.logo img {
  height: auto;
  max-height: 52px;
  width: auto;
  border-radius: 8px;
}

.logo svg {
  width: 28px;
  height: 28px;
  fill: #ff3e45;
  transition: fill 0.3s ease;
}

.sidebar nav {
  margin-bottom: 36px;
  flex-shrink: 0;
}

.sidebar nav a {
  display: flex;
  align-items: center;
  gap: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 8px;
  padding: 7px 12px;
  border-radius: 10px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.sidebar nav a:hover {
  color: #ff3e45;
  background-color: rgba(255, 62, 69, 0.1);
  transform: translateX(4px);
}

.sidebar nav a.active {
  color: #ff3e45;
  background-color: rgba(255, 62, 69, 0.15);
  font-weight: 600;
}

.sidebar nav a svg {
  width: 18px;
  height: 18px;
  fill: currentColor;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

/* Tags */
.tags {
  margin-top: auto;
}

.tags h3 {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 14px;
  color: white;
  user-select: none;
}

.tag-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
}

.tag-list button {
  border: none;
  background: none;
  padding: 0;
  text-align: left;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #bfbfbf;
  transition: color 0.3s;
}

.tag-list button:hover,
.tag-list button.active {
  color: #ff3e45;
}

.tag-color {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.tag-color.selfhosting {
  background-color: #ff6900;
}

.tag-color.hacking {
  background-color: #4d90fe;
}

.tag-color.cve {
  background-color: #ff3e45;
}

/* Sidebar footer */
.sidebar-footer {
  margin-top: 40px;
  font-size: 13px;
  display: flex;
  gap: 16px;
  color: #666;
  user-select: none;
}

.sidebar-footer a {
  color: #666;
  text-decoration: none;
  transition: color 0.25s;
}

.sidebar-footer a:hover {
  color: #999;
}

/* Top filter & search and user */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  gap: 20px;
  flex-wrap: wrap;
}

.top-bar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-wrapper {
  position: relative;
  flex: 1;
  max-width: 380px;
}

.search-wrapper input {
  width: 100%;
  background-color: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 12px 44px 12px 16px;
  color: var(--text-primary);
  font-size: 15px;
  transition: all 0.3s ease;
  font-family: inherit;
}

.search-wrapper input:focus {
  outline: none;
  border-color: #ff3e45;
  box-shadow: 0 0 0 3px rgba(255, 62, 69, 0.1);
  background-color: var(--bg-primary);
}

.search-wrapper input::placeholder {
  color: var(--text-secondary);
  font-weight: 400;
}

.search-wrapper svg {
  position: absolute;
  top: 50%;
  right: 14px;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  fill: var(--text-secondary);
  pointer-events: none;
  transition: fill 0.3s ease;
}

.user-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--bg-secondary);
  border: 2px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-icon:hover {
  background-color: #ff3e45;
  border-color: #ff3e45;
  transform: scale(1.05);
}

.user-icon svg {
  fill: var(--text-primary);
  width: 20px;
  height: 20px;
  transition: fill 0.3s ease;
}

.user-icon:hover svg {
  fill: white;
}

/* Theme Toggle Button */
.toggle-color-scheme-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0px 10px;
  border: 2px solid var(--border-color);
  border-radius: 99px;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  height: 41px;
}

.toggle-color-scheme-button:hover {
  border-color: #ff3e45;
  background-color: rgba(255, 62, 69, 0.1);
}

.toggle-color-scheme-button .icon {
  width: 18px;
  height: 18px;
  transition: all 0.3s ease;
}

.toggle-color-scheme-button .label {
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  display: none!important;
}

/* Category Tabs */
.category-tabs {
  display: flex;
  gap: 12px;
  margin-bottom: 32px;
  flex-wrap: wrap;
  padding: 4px;
  background-color: var(--bg-secondary);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  width: fit-content;
}

.category-tabs button {
  background: none;
  border: none;
  padding: 10px 20px;
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
}

.category-tabs button:hover {
  color: var(--text-primary);
  background-color: #ff3e45;
}

.category-tabs button.active {
  background-color: #ff3e45;
  color: white;
  box-shadow: 0 2px 8px rgba(255, 62, 69, 0.3);
  transform: translateY(-1px);
}

/* Main featured post */
.featured-post {
  display: flex;
  background: var(--bg-tertiary);
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-height: 480px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid var(--border-color);
}

.featured-post:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.light-theme .featured-post {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.light-theme .featured-post:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}
.light-theme .post-title {
  color: #000;
}


.dark-theme .icon-moon{display: none!important;}
.dark-theme .icon-sun{display: block!important;}
.light-theme .icon-sun{display: none!important;}
.light-theme .icon-moon{display: block!important;}

.featured-content {
  padding: 28px 32px;
  flex: 1 1 60%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #eee;
  padding-top: 60px;
  position: relative;
}

.featured-content .category {
  font-size: 13px;
  color: #ff6900;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 12px;
  user-select: none;
}

.featured-content h2 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 10px;
  line-height: 1.25;
}

.featured-content p.desc {
  color: #ccc;
  font-weight: 500;
  font-size: 15px;
  margin-bottom: 20px;
  line-height: 1.4;
}

.btn-readmore {
  width: max-content;
  font-size: 14px;
  font-weight: 600;
  background-color: #fff;
  color: #121212;
  border-radius: 20px;
  padding: 8px 18px;
  box-shadow: 0 3px 7px #ff3e45bb;
  border: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: background-color 0.3s, box-shadow 0.3s;
}

.btn-readmore:hover {
  background-color: #ff3e45;
  color: white;
  box-shadow: 0 0 18px #ff3e45ee;
}

.btn-readmore svg {
  width: 14px;
  height: 14px;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

.featured-image {
  flex: 1 1 40%;
  position: relative;
}

.featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reading-time {
  position: absolute;
  bottom: 14px;
  right: 14px;
  background: rgba(0, 0, 0, 0.7);
  font-size: 11px;
  color: white;
  padding: 4px 10px;
  border-radius: 14px;
  font-weight: 600;
  user-select: none;
}

.featured-nav {
  margin-top: 12px;
  color: #bbb;
  font-size: 20px;
  display: flex;
  gap: 12px;
  cursor: pointer;
  user-select: none;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  bottom: 1px;
  right: 10px;
}

.featured-nav svg {
  fill: none;
  stroke: currentColor;
  stroke-width: 2.2;
  stroke-linecap: round;
  stroke-linejoin: round;
  width: 22px;
  height: 22px;
  transition: color 0.3s;
}

.featured-nav svg:hover {
  color: #ff3e45;
}

/* Cards grid */
.post-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 28px;
  margin-bottom: 52px;
}

/* Post card */
.post-card {
  background-color: var(--card-bg);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid var(--border-color);
  height: fit-content;
}

.post-card > div {
  aspect-ratio: 16 / 9;
  background-color: var(--color-background-200);
  border-radius: var(--radius-4);
  overflow: hidden;
  perspective: 1000px;
  position: relative;
  transition: box-shadow 0.5s;
  z-index: 1;
}

.post-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25);
}

.light-theme .post-card {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.06);
}

.light-theme .post-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.post-card img {
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  object-fit: cover;
  flex-shrink: 0;
}

.post-card .card-body {
  padding: 16px 20px 18px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.post-card .reading-time {
  position: absolute;
  bottom: 8px;
  right: 8px;
  padding: 3px 8px;
  font-size: 11px;
  color: #eee;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 13px;
  user-select: none;
}

.post-info {
  margin-top: 8px;
  font-size: 13px;
  color: #999;
}

.post-info span:not(:last-child)::after {
  content: "•";
  margin: 0 6px;
  color: #555;
}

/* Post title & details */
.post-title {
  font-weight: 600;
  color: #eee;
  font-size: 17px;
  margin-bottom: 8px;
  line-height: 1.2;
}

.post-subtext {
  font-size: 13px;
  color: #bbb;
  font-weight: 500;
  opacity: 0.8;
}

/* Post small items: author avatar and name */
.post-author {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #ff3e45;
  font-weight: 600;
  margin-bottom: 4px;
  user-select: none;
}

.author-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #ff3e45;
  overflow: hidden;
  flex-shrink: 0;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Section headers */
section {
  margin-bottom: 0;
}

section > h2 {
  font-weight: 700;
  font-size: 20px;
  color: white;
  margin-bottom: 6px;
  user-select: none;
}

section > p.description {
  font-weight: 500;
  font-size: 14px;
  color: #bbb;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.section-header h2 {
  margin: 0;
}

.view-more-btn {
  font-size: 13px;
  font-weight: 600;
  color: #888;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s;
  user-select: none;
  display: flex;
  align-items: center;
  gap: 6px;
}

.view-more-btn:hover {
  color: #ff3e45;
}

.view-more-btn svg {
  width: 16px;
  height: 16px;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}

/* LOAD MORE button */
.btn-loadmore {
  margin: 0 auto 48px;
  display: block;
  padding: 14px 36px;
  border-radius: 32px;
  font-weight: 600;
  font-size: 14px;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  user-select: none;
}

.btn-loadmore:hover {
  background-color: #ff3e45;
  color: white;
  border-color: #ff3e45;
  box-shadow: 0 6px 24px rgba(255, 62, 69, 0.3);
  transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1001;
  width: 44px;
  height: 44px;
  background-color: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
  background-color: #ff3e45;
  border-color: #ff3e45;
}

.mobile-menu-toggle span {
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  border-radius: 1px;
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover span {
  background-color: white;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Overlay */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    grid-template-columns: 260px 1fr;
  }

  .main {
    padding: 28px 32px 40px;
  }

  .post-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
  }
}

@media (max-width: 968px) {
  .mobile-menu-toggle {
    display: flex;
  }

  .mobile-overlay {
    display: block;
  }

  .container {
    grid-template-columns: 1fr;
    height: 100vh;
  }

  .sidebar {
    position: fixed;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transition: left 0.3s ease;
    border-right: none;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
    padding: 28px 24px 32px;
    width: 100%;
  }

  .sidebar.open {
    left: 0;
  }

  .main {
    padding: 80px 20px 32px;
    overflow-y: auto;
    height: 100vh;
  }

  .top-bar {
    margin-bottom: 24px;
  }
  .top-bar-desktop {
    display: none;
  }

  .featured-post {
    flex-direction: column;
    min-height: auto;
  }

  .featured-content {
    padding: 24px;
  }

  .featured-image {
    height: 220px;
    order: -1;
  }

  .post-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }

  .category-tabs {
    width: 100%;
    justify-content: flex-start;
    overflow-x: auto;
    padding: 8px;
  }
}

@media (max-width: 640px) {
  .main {
    padding: 80px 16px 24px;
  }

  .top-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    margin-bottom: 20px;
  }

  .search-wrapper {
    max-width: 100%;
  }

  .sidebar {
    width: 100%;
    padding: 24px 20px 32px;
  }

  .category-tabs {
    gap: 12px;
    margin-bottom: 20px;
  }

  .category-tabs button {
    padding: 8px 12px;
    font-size: 12px;
  }

  .featured-post {
    margin-bottom: 28px;
    border-radius: 12px;
  }

  .featured-content {
    padding: 20px;
  }

  .featured-content h2 {
    font-size: 18px;
  }

  .post-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 32px;
  }

  .post-card {
    border-radius: 12px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

.popup {
  display: flex;
  pointer-events: none;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  height: 100%;
  left: 0;
  opacity: 0;
  overflow-x: auto;
  overflow-y: hidden;
  overflow: auto hidden;
  position: fixed;
  top: 0;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  visibility: hidden;
  width: 100%;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
}

.popup.popup-hide {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

/* Search Popup Styles */
.popup-search .popup-container {
  max-width: 600px;
  width: 90%;
  margin: 5% auto;
  background: var(--bg-secondary);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.popup-search .popup-head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-tertiary);
}

.popup-search .popup-head h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.popup-search .popup-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-input-wrapper {
  position: relative;
}

.search-input-wrapper input {
  width: 100%;
  padding: 16px 50px 16px 20px;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.search-input-wrapper input:focus {
  outline: none;
  border-color: var(--text-accent);
  box-shadow: 0 0 0 3px rgba(255, 62, 69, 0.1);
}

.search-input-wrapper .search-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

.search-results {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
}

.search-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-secondary);
}

.search-placeholder svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.search-placeholder p {
  margin: 0;
  font-size: 14px;
}

.search-result-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  cursor: pointer;
  margin-bottom: 8px;
}

.search-result-item:hover {
  background-color: var(--bg-tertiary);
}

.search-result-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.search-result-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.search-result-content {
  flex: 1;
}

.search-result-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: 4px;
  line-height: 1.3;
}

.search-result-meta {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.search-result-excerpt {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.search-no-results {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.search-no-results p {
  margin: 0;
  font-size: 14px;
}

/* Responsive search popup */
@media (max-width: 600px) {
  .search-toggle,
  .header-menu-right .user-login {
    display: none !important;
  }

  .popup-search .popup-container {
    width: 95%;
    margin: 2% auto;
    max-height: 90vh;
  }

  .popup-search .popup-head {
    padding: 16px 20px;
  }

  .popup-search .popup-body {
    padding: 20px;
  }

  .search-input-wrapper input {
    padding: 14px 45px 14px 16px;
    font-size: 16px;
  }
}

section {
  z-index: 99;
}

.popup-container {
  z-index: 999;
}

.popup-show {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  width: 100%;
  z-index: 99999;
}

.popup-container {
  background-color: var(--color-background);
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  overflow: hidden auto;
  position: absolute;
  top: 0;
  transition: transform 0.6s cubic-bezier(0.2, 1, 0.2, 1);
  transition-delay: 0.3s;
}

.popup-navigation .popup-container {
  left: 0;
  /*transform: translateX(-40px);*/
  width: 320px;
  width: calc(var(--sidebar--width) + var(--spacing-6));
}

.header-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-menu .toggle-sidebar {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.header-menu .toggle-sidebar:hover {
  background-color: var(--bg-secondary);
}

.header-menu .toggle-sidebar svg {
  width: 20px;
  height: 20px;
  stroke: currentColor;
  fill: none;
}

.top-bar-right {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}

.sidebar .toggle-color-scheme-button,
.sidebar .dropdown-toggle,
.sidebar .search-toggle {
  display: none;
}
.sidebar-body{
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    flex-wrap: nowrap;
    height: 100vh;
}
/* Responsive */
@media (max-width: 900px) {
  .header-menu-right {
    display: flex;
    gap: 3px;
  }

  .navbar {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    width: 100%;
  }

  .sidebar .toggle-color-scheme-button,
  .sidebar .dropdown-toggle,
  .sidebar .search-toggle {
    display: block;
  }

  .header-menu .toggle-sidebar {
    display: block;
  }

  .container {
    grid-template-columns: 1fr;
    height: auto;
  }

  .sidebar {
    height: auto;
    border-right: none;
    padding-bottom: 12px;
    flex-direction: row;
    overflow-x: auto;
  }

  .logo {
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .sidebar nav {
    display: flex;
    gap: 0px;
    margin-bottom: 0;
  }

  .tags {
    display: none;
  }

  .main {
    padding: 24px 18px 40px;
  }

  .featured-post {
    flex-direction: column;
    min-height: auto;
  }

  .featured-content {
    padding: 18px 20px 24px;
  }

  .featured-image {
    height: 180px;
  }

  .post-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
  }

  .navbar {
    display: flex;
  }

  .top-bar-desktop,
  .sidebar-body {
    display: none;
  }

  .popup-show .sidebar {
    height: auto;
    border-right: none;
    padding-bottom: 12px;
    flex-direction: column;
    overflow-x: auto;
    min-height: 100vh;
  }

  .sidebar nav {
    display: flex;
    flex-direction: column;
  }

  .popup-head {
    align-items: center;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    display: flex;
    gap: var(--spacing-5);
    min-height: var(--navbar--min-height);
    padding: 0 calc(var(--spacing-8) - var(--spacing-1));
    position: sticky;
    top: 0;
    z-index: 3;
    margin-bottom: 15px;
  }

  .button-icon.button-sm {
    min-height: 32px;
    min-width: 32px;
    padding: 0.375rem;
    color: #fff;
    background: #212121;
    border-radius: 2rem;
    border: 1px solid #423838;
    display: flex;
  }

  .header-menu {
    display: flex;
    flex-direction: row;
  }

  .category-tabs {
    display: inline;
    gap: 16px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    flex-direction: row;
    white-space: nowrap;
    overflow-x: scroll;
  }
}

@media (max-width: 440px) {
  .top-bar {
    flex-direction: column;
    gap: 18px;
  }

  .search-wrapper {
    max-width: 100%;
    flex-grow: 1;
  }
}
