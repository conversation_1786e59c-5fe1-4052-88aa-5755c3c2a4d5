# Hướng dẫn thiết lập Category ID cho UpCode Theme

## 📋 Tổng quan

Theme UpCode đã được thiết lập với các section động cho các danh mục bài viết. <PERSON>ạn cần thay đổi ID của các danh mục trong file `index.php` để phù hợp với danh mục trên website của bạn.

## 🔧 Cách tìm Category ID

### Phương pháp 1: Trong WordPress Admin
1. Đăng nhập vào WordPress Admin
2. Vào **Posts > Categories**
3. Hover chuột lên tên danh mục
4. Xem URL ở thanh trạng thái, ID sẽ hiển thị như: `tag_ID=123`

### Phương pháp 2: Chỉnh sửa danh mục
1. Vào **Posts > Categories**
2. Click **Edit** trên danh mục
3. Xem URL trên thanh địa chỉ: `...term.php?taxonomy=category&tag_ID=123`

### Phương pháp 3: Sử dụng code PHP
Thêm code này vào `functions.php` tạm thời để hiển thị tất cả category ID:

```php
// Tạm thời thêm vào functions.php để xem category ID
function show_category_ids() {
    if (is_admin()) {
        $categories = get_categories();
        echo '<div style="background: #fff; padding: 10px; margin: 10px;">';
        echo '<h3>Category IDs:</h3>';
        foreach ($categories as $cat) {
            echo '<p>' . $cat->name . ' - ID: ' . $cat->term_id . '</p>';
        }
        echo '</div>';
    }
}
add_action('admin_notices', 'show_category_ids');
```

## 📝 Cách thay đổi Category ID trong theme

### File cần chỉnh sửa: `index.php`

Tìm và thay đổi các dòng sau:

#### 1. Section Selfhosting (dòng ~151)
```php
// Thay đổi số 1 thành ID danh mục Selfhosting của bạn
$selfhosting_cat_id = 1; // Thay đổi ID này
```

#### 2. Section Hacking (dòng ~223)
```php
// Thay đổi số 2 thành ID danh mục Hacking của bạn
$hacking_cat_id = 2; // Thay đổi ID này
```

#### 3. Section CVE/CVD (dòng ~295)
```php
// Thay đổi số 3 thành ID danh mục CVE/CVD của bạn
$cve_cat_id = 3; // Thay đổi ID này
```

## 🎨 Tùy chỉnh màu sắc danh mục

Trong file `functions.php`, tìm function `upcode_get_post_categories_with_colors()` để thay đổi màu:

```php
$category_colors = array(
    'selfhosting' => '#ff6900',  // Màu cam
    'hacking' => '#4d90fe',      // Màu xanh dương
    'cve' => '#ff3e45',          // Màu đỏ
    'cvd' => '#ff3e45',          // Màu đỏ
);
```

**Lưu ý:** Màu sắc dựa trên slug của danh mục, không phải ID.

## 📚 Ví dụ thực tế

Giả sử bạn có các danh mục:
- **Technology** (ID: 5, slug: technology)
- **Security** (ID: 8, slug: security)  
- **Tutorials** (ID: 12, slug: tutorials)

### Bước 1: Cập nhật ID trong index.php
```php
// Section 1
$selfhosting_cat_id = 5; // Technology

// Section 2  
$hacking_cat_id = 8; // Security

// Section 3
$cve_cat_id = 12; // Tutorials
```

### Bước 2: Cập nhật màu sắc (nếu muốn)
```php
$category_colors = array(
    'technology' => '#00a8ff',   // Xanh dương
    'security' => '#ff3838',     // Đỏ
    'tutorials' => '#00d2d3',    // Xanh lá
);
```

## 🔄 Chức năng AJAX Load More

Theme đã tích hợp sẵn chức năng load more bằng AJAX:

### Cho trang chủ (Recent Posts)
- Tự động hoạt động với nút "Load More"
- Load 6 bài viết mỗi lần

### Cho từng danh mục (tùy chọn)
Có thể sử dụng function JavaScript:
```javascript
// Ví dụ: Load more cho danh mục Technology (ID: 5)
UpCodeTheme.loadMoreCategoryPosts(5, 'technology-posts-grid', 'load-more-technology');
```

## ⚠️ Lưu ý quan trọng

1. **Backup trước khi chỉnh sửa**: Luôn backup file trước khi thay đổi
2. **Test trên staging**: Test trên môi trường staging trước khi deploy
3. **Kiểm tra featured image**: Các bài viết cần có featured image để hiển thị
4. **Cache**: Clear cache sau khi thay đổi
5. **Child theme**: Nên sử dụng child theme cho customization lớn

## 🚀 Sau khi thiết lập

1. Kiểm tra trang chủ xem các section hiển thị đúng
2. Test chức năng "Load More"
3. Kiểm tra responsive trên mobile
4. Verify màu sắc danh mục hiển thị chính xác

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra WordPress error log
2. Đảm bảo category ID tồn tại
3. Verify danh mục có bài viết với featured image
4. Check browser console cho lỗi JavaScript

---

**Chúc bạn thiết lập thành công! 🎉**
