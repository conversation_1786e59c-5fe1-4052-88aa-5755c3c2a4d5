// Blog Layout JavaScript - Sidebar Toggle & Theme Switcher

// DOM Elements
const toggleSidebarBtn = document.querySelector('.toggle-sidebar');
const popup = document.querySelector('.popup-navigation');
const searchPopup = document.querySelector('#search-popup');
const searchToggleBtns = document.querySelectorAll('.search-toggle');
const searchInput = document.getElementById('search-input');
const searchResults = document.getElementById('search-results');
const popupCloses = document.querySelectorAll('.popup-close');
const themeToggleBtns = document.querySelectorAll('.toggle-color-scheme-button');
const body = document.body;
const html = document.documentElement;

// Theme management
class ThemeManager {
    constructor() {
        this.currentTheme = this.getStoredTheme() || 'dark';
        this.initTheme();
    }

    getStoredTheme() {
        return localStorage.getItem('blog-theme');
    }

    setStoredTheme(theme) {
        localStorage.setItem('blog-theme', theme);
    }

    initTheme() {
        this.applyTheme(this.currentTheme);
        this.updateThemeButtons();
    }

    applyTheme(theme) {
        // Remove existing theme classes
        html.classList.remove('light-theme', 'dark-theme');
        
        // Apply new theme
        if (theme === 'light') {
            html.classList.add('light-theme');
            this.applyLightTheme();
        } else {
            html.classList.add('dark-theme');
            this.applyDarkTheme();
        }
        
        this.currentTheme = theme;
        this.setStoredTheme(theme);
    }

    applyLightTheme() {
        const lightColors = {
            '--bg-primary': '#ffffff',
            '--bg-secondary': '#f8f9fa',
            '--bg-tertiary': '#ffffff',
            '--text-primary': '#212529',
            '--text-secondary': '#6c757d',
            '--text-accent': '#ff3e45',
            '--border-color': '#dee2e6',
            '--card-bg': '#ffffff',
            '--sidebar-bg': '#f8f9fa'
        };

        Object.entries(lightColors).forEach(([property, value]) => {
            html.style.setProperty(property, value);
        });

        // Update body styles for light theme
        body.style.backgroundColor = '#ffffff';
        body.style.color = '#212529';
        
        // Update specific elements
        this.updateElementsForTheme('light');
    }

    applyDarkTheme() {
        const darkColors = {
            '--bg-primary': '#121212',
            '--bg-secondary': '#181818',
            '--bg-tertiary': '#1e1e1e',
            '--text-primary': '#e0e0e0',
            '--text-secondary': '#bfbfbf',
            '--text-accent': '#ff3e45',
            '--border-color': '#2c2c2c',
            '--card-bg': '#1a1a1a',
            '--sidebar-bg': '#181818'
        };

        Object.entries(darkColors).forEach(([property, value]) => {
            html.style.setProperty(property, value);
        });

        // Update body styles for dark theme
        body.style.backgroundColor = '#121212';
        body.style.color = '#e0e0e0';
        
        // Update specific elements
        this.updateElementsForTheme('dark');
    }

    updateElementsForTheme(theme) {
        const sidebar = document.querySelector('.sidebar');
        const main = document.querySelector('.main');
        const cards = document.querySelectorAll('.post-card, .featured-post');
        const searchInput = document.querySelector('.search-wrapper input');

        if (theme === 'light') {
            if (sidebar) sidebar.style.backgroundColor = '#f8f9fa';
            if (main) main.style.backgroundColor = '#ffffff';
            
            cards.forEach(card => {
                card.style.backgroundColor = '#ffffff';
                card.style.boxShadow = '0 3px 7px rgba(0, 0, 0, 0.1)';
            });
            
            if (searchInput) {
                searchInput.style.backgroundColor = '#f8f9fa';
                searchInput.style.color = '#212529';
            }
        } else {
            if (sidebar) sidebar.style.backgroundColor = '#181818';
            if (main) main.style.backgroundColor = '#141414';
            
            cards.forEach(card => {
                if (card.classList.contains('featured-post')) {
                    card.style.backgroundColor = '#1e1e1e';
                } else {
                    card.style.backgroundColor = '#1a1a1a';
                }
                card.style.boxShadow = '0 3px 7px rgba(0, 0, 0, 0.7)';
            });
            
            if (searchInput) {
                searchInput.style.backgroundColor = '#222';
                searchInput.style.color = '#ddd';
            }
        }
    }

    updateThemeButtons() {
        themeToggleBtns.forEach(btn => {
            const lightLabel = btn.querySelector('.label-light');
            const darkLabel = btn.querySelector('.label-dark');
            const moonIcon = btn.querySelector('.icon-moon');
            const sunIcon = btn.querySelector('.icon-sun');

            if (this.currentTheme === 'light') {
                if (lightLabel) lightLabel.style.display = 'inline';
                if (darkLabel) darkLabel.style.display = 'none';
                if (moonIcon) moonIcon.style.display = 'none';
                if (sunIcon) sunIcon.style.display = 'inline';
            } else {
                if (lightLabel) lightLabel.style.display = 'none';
                if (darkLabel) darkLabel.style.display = 'inline';
                if (moonIcon) moonIcon.style.display = 'inline';
                if (sunIcon) sunIcon.style.display = 'none';
            }
        });
    }

    toggle() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        this.updateThemeButtons();
    }
}

// Search management
class SearchManager {
    constructor() {
        this.isOpen = false;
        this.searchData = this.generateMockData();
        this.init();
    }

    init() {
        // Ensure search popup starts hidden
        if (searchPopup) {
            searchPopup.classList.add('popup-hide');
            searchPopup.classList.remove('popup-show');
            searchPopup.setAttribute('aria-hidden', 'true');
        }
    }

    generateMockData() {
        // Mock data based on the posts in the HTML
        return [
            {
                title: "The Importance of CPE for CVE Vulnerability Management and Research",
                excerpt: "Understanding Common Platform Enumeration (CPE) and its crucial role in vulnerability management and cybersecurity research.",
                category: "CVE / CVD",
                author: "LRVT",
                date: "May 21, 2025",
                readTime: "8 min read",
                image: "https://images.unsplash.com/photo-1557683316-973673baf926?auto=format&fit=crop&w=400&q=80",
                tags: ["CVE", "CVD", "Hacking"]
            },
            {
                title: "Visualizing Traefik v3 Metrics and HTTP Logs in Grafana",
                excerpt: "Learn how to set up comprehensive monitoring for Traefik v3 using Grafana dashboards and metrics visualization.",
                category: "Selfhosting",
                author: "LRVT",
                date: "Apr 27, 2025",
                readTime: "7 min read",
                image: "https://images.unsplash.com/photo-1516251193007-45ef944ab0c6?auto=format&fit=crop&w=400&q=80",
                tags: ["Selfhosting", "Traefik", "Grafana"]
            },
            {
                title: "Grafana Dashboard for CrowdSec Cyber Threat Intelligence Insights",
                excerpt: "Building a Dockerized Grafana Dashboard for CrowdSec Cyber Threat Intelligence (TI) Insights using VictoriaMetrics.",
                category: "Selfhosting",
                author: "LRVT",
                date: "Apr 23, 2025",
                readTime: "8 min read",
                image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80",
                tags: ["Selfhosting", "CrowdSec", "Grafana"]
            },
            {
                title: "Configuring CrowdSec with Traefik",
                excerpt: "Complete guide to integrating CrowdSec security engine with Traefik reverse proxy for enhanced protection.",
                category: "Selfhosting",
                author: "LRVT",
                date: "Oct 22, 2024",
                readTime: "23 min read",
                image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80",
                tags: ["Selfhosting", "CrowdSec", "Traefik"]
            },
            {
                title: "Rootful or Rootless: The Current State of iOS Jailbreaking for Pentesters",
                excerpt: "Comprehensive analysis of modern iOS jailbreaking techniques and their applications in penetration testing.",
                category: "Hacking",
                author: "LRVT",
                date: "Feb 24, 2024",
                readTime: "13 min read",
                image: "https://images.unsplash.com/photo-1526378721910-178ac89b3560?auto=format&fit=crop&w=400&q=80",
                tags: ["Hacking", "iOS", "Jailbreaking"]
            },
            {
                title: "Bypassing Microsoft MDE Using Silver C2 Stagers",
                excerpt: "Advanced techniques for evading Microsoft Defender for Endpoint using Silver C2 framework and custom stagers.",
                category: "Hacking",
                author: "LRVT",
                date: "Feb 9, 2024",
                readTime: "10 min read",
                image: "https://images.unsplash.com/photo-1534452203293-494d7ddbf7e0?auto=format&fit=crop&w=400&q=80",
                tags: ["Hacking", "MDE", "C2"]
            },
            {
                title: "XSS Vulnerability in WeddingShare <= v1.4.7",
                excerpt: "Detailed analysis of a cross-site scripting vulnerability discovered in WeddingShare application.",
                category: "CVE / CVD",
                author: "LRVT",
                date: "Jan 14, 2025",
                readTime: "1 min read",
                image: "https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?auto=format&fit=crop&w=400&q=80",
                tags: ["CVE", "CVD", "XSS"]
            },
            {
                title: "IDOR Vulnerability Grants Unauthorized Access to Paid eBooks at IGI Global",
                excerpt: "Investigation of an Insecure Direct Object Reference vulnerability that allowed unauthorized access to premium content.",
                category: "CVE / CVD",
                author: "LRVT",
                date: "Sep 9, 2022",
                readTime: "8 min read",
                image: "https://images.unsplash.com/photo-1512820790803-83ca734da794?auto=format&fit=crop&w=400&q=80",
                tags: ["CVE", "CVD", "IDOR"]
            }
        ];
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        if (searchPopup) {
            searchPopup.classList.remove('popup-hide');
            searchPopup.classList.add('popup-show');
            searchPopup.setAttribute('aria-hidden', 'false');
            this.isOpen = true;
            
            // Focus on search input
            setTimeout(() => {
                if (searchInput) {
                    searchInput.focus();
                }
            }, 100);
            
            // Prevent body scroll
            body.style.overflow = 'hidden';
        }
    }

    close() {
        if (searchPopup) {
            searchPopup.classList.add('popup-hide');
            searchPopup.classList.remove('popup-show');
            searchPopup.setAttribute('aria-hidden', 'true');
            this.isOpen = false;
            
            // Clear search
            if (searchInput) {
                searchInput.value = '';
            }
            this.showPlaceholder();
            
            // Restore body scroll
            body.style.overflow = '';
        }
    }

    search(query) {
        if (!query || query.trim().length < 2) {
            this.showPlaceholder();
            return;
        }

        const results = this.searchData.filter(post => {
            const searchText = `${post.title} ${post.excerpt} ${post.category} ${post.tags.join(' ')}`.toLowerCase();
            return searchText.includes(query.toLowerCase());
        });

        this.displayResults(results, query);
    }

    showPlaceholder() {
        if (searchResults) {
            searchResults.innerHTML = `
                <div class="search-placeholder">
                    <svg width="48" height="48" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.5 17.5L13.875 13.875M15.8333 9.16667C15.8333 12.8486 12.8486 15.8333 9.16667 15.8333C5.48477 15.8333 2.5 12.8486 2.5 9.16667C2.5 5.48477 5.48477 2.5 9.16667 2.5C12.8486 2.5 15.8333 5.48477 15.8333 9.16667Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                    <p>Start typing to search for posts...</p>
                </div>
            `;
        }
    }

    displayResults(results, query) {
        if (!searchResults) return;

        if (results.length === 0) {
            searchResults.innerHTML = `
                <div class="search-no-results">
                    <p>No posts found for "${query}". Try different keywords.</p>
                </div>
            `;
            return;
        }

        const resultsHTML = results.map(post => `
            <div class="search-result-item" onclick="window.location.href='#'">
                <div class="search-result-image">
                    <img src="${post.image}" alt="${post.title}" />
                </div>
                <div class="search-result-content">
                    <div class="search-result-title">${this.highlightText(post.title, query)}</div>
                    <div class="search-result-meta">${post.date} · ${post.category} · ${post.readTime}</div>
                    <div class="search-result-excerpt">${this.highlightText(post.excerpt, query)}</div>
                </div>
            </div>
        `).join('');

        searchResults.innerHTML = resultsHTML;
    }

    highlightText(text, query) {
        if (!query) return text;
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark style="background-color: var(--text-accent); color: white; padding: 1px 2px; border-radius: 2px;">$1</mark>');
    }
}

// Sidebar management
class SidebarManager {
    constructor() {
        this.isOpen = false;
        this.init();
    }

    init() {
        // Ensure popup starts hidden
        if (popup) {
            popup.classList.add('popup-hide');
            popup.classList.remove('popup-show');
            popup.setAttribute('aria-hidden', 'true');
        }
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        if (popup) {
            popup.classList.remove('popup-hide');
            popup.classList.add('popup-show');
            popup.setAttribute('aria-hidden', 'false');
            this.isOpen = true;
            
            // Update toggle button aria-expanded
            if (toggleSidebarBtn) {
                toggleSidebarBtn.setAttribute('aria-expanded', 'true');
            }
            
            // Prevent body scroll
            body.style.overflow = 'hidden';
        }
    }

    close() {
        if (popup) {
            popup.classList.add('popup-hide');
            popup.classList.remove('popup-show');
            popup.setAttribute('aria-hidden', 'true');
            this.isOpen = false;
            
            // Update toggle button aria-expanded
            if (toggleSidebarBtn) {
                toggleSidebarBtn.setAttribute('aria-expanded', 'false');
            }
            
            // Restore body scroll
            body.style.overflow = '';
        }
    }
}

// Initialize managers
const themeManager = new ThemeManager();
const sidebarManager = new SidebarManager();
const searchManager = new SearchManager();

// Event listeners
if (toggleSidebarBtn) {
    toggleSidebarBtn.addEventListener('click', () => {
        sidebarManager.toggle();
    });
}

// Toggle search popup
if (searchToggleBtns) {
    searchToggleBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            searchManager.toggle();
        });
    });
}

// Search input
if (searchInput) {
    searchInput.addEventListener('input', (e) => {
        searchManager.search(e.target.value);
    });
    
    // Handle enter key
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchManager.search(e.target.value);
        }
    });
}

// Close popup
if (popupCloses) {
    popupCloses.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const popup = e.target.closest('.popup');
            if (popup && popup.id === 'search-popup') {
                searchManager.close();
            } else {
                sidebarManager.close();
            }
        });
    });
}

// Theme toggle buttons
if (themeToggleBtns) {
    themeToggleBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            themeManager.toggle();
        });
    });
}

// Close sidebar when clicking outside
if (popup) {
    popup.addEventListener('click', (e) => {
        if (e.target === popup) {
            sidebarManager.close();
        }
    });
}

if (searchPopup) {
    searchPopup.addEventListener('click', (e) => {
        if (e.target === searchPopup) {
            searchManager.close();
        }
    });
}

// Close popups with ESC key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        if (searchManager.isOpen) {
            searchManager.close();
        } else if (sidebarManager.isOpen) {
            sidebarManager.close();
        }
    }
});

// Handle window resize
window.addEventListener('resize', () => {
    // Close sidebar on desktop view
    if (window.innerWidth > 900 && sidebarManager.isOpen) {
        sidebarManager.close();
    }
});

// Smooth scroll for anchor links
document.addEventListener('click', (e) => {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Export for potential external use
window.BlogLayout = {
    themeManager,
    sidebarManager
};