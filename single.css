/* CSS Variables */
:root {
    --color-primary: #282828;
    --color-primary-dark: #005a99;
    --color-text: #333333;
    --color-text-secondary: #666666;
    --color-background: #ffffff;
    --color-background-secondary: #f8f9fa;
    --color-background-tertiary: #e9ecef;
    --color-border: #e5e5e5;
    --spacing-1: 0.25rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-9: 2.25rem;
    --font-small: 0.875rem;
    --font-weight-medium: 500;
    --avatar-large--size: 2.875rem;
    --featured-image--max-width: 1200px;
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
    :root {
        --color-text: #ffffff;
        --color-text-secondary: #cccccc;
        --color-background: #1a1a1a;
        --color-background-secondary: #2d2d2d;
        --color-background-tertiary: #404040;
        --color-border: #404040;
    }
}

.post-header {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    position: relative;
}

.content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 0 auto;
    max-width: 1200px;
    scroll-margin: 58px;
    width: 100%;
}


 .post-header-info {
    display: flex;
    flex: 1 0 0%;
    flex-direction: column;
    gap: var(--spacing-1);
    justify-content: center;
    min-height: var(--avatar-large--size);
    position: relative;
}

.post-featured {
    margin-top: calc(var(--spacing-9) * -1);
}

.post-featured {
    margin: 0 auto;
    max-width: var(--featured-image--max-width);
    position: relative;
    width: 100%;
    z-index: 1;
}

.avatar-large {
    height: 2.875rem;
    line-height: 2.875rem;
    width: 2.875rem;
}

.avatar-large img {
width: 46px;
}


.post-meta {
    min-height: auto;
    padding-left: 0;
    position: static;
}

.post-meta .post-meta-author{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.post-meta {
    align-items: center;
    color: grey;
    display: flex;
    flex: 1 0 0%;
    flex-wrap: wrap;
    font-variation-settings: "wght" var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
    gap: 6px;
    list-style: none;
    margin: 0;
    min-height: var(--avatar-large--size);
    font-size: 10px;
    position: relative;
}


.post-template-default .post-meta li:first-child {
    flex: 0 0 auto;
}

.post-meta li {
    align-items: center;
    display: flex;
    gap: 0 var(--spacing-4);
    list-style: none;
}

.post-template-default .post-header-buttons {
    align-self: flex-end;
}


.post-header-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-3) var(--spacing-5);
}

/* Post Featured Image */
.post-featured {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.post-featured:hover {
    transform: translateY(-2px);
}

.post-featured-image {
    margin: 0;
    position: relative;
}

.post-featured-image img {
    aspect-ratio: 21 / 9;
    display: block;
    height: auto;
    width: 100%;
}

.post-featured-image figcaption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 20px;
    font-size: 0.875rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.post-featured:hover figcaption {
    opacity: 1;
}

/* Post Content Grid */
.post-content-grid {
    display: grid;
    grid-template-columns: 70% 30%;
    gap: 1rem;
    margin-top: 0rem;
    align-items: start;
}

.post-content-wrapper {
    min-width: 0;
}

/* Post Tags */
.post-tags {
    margin-bottom: 0rem;
}

.post-tags ul {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.post-tag {
    background: var(--color-primary, #007acc);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.post-tag:hover {
    background: var(--color-primary-dark, #005a99);
    transform: translateY(-1px);
}

/* Post Content */
.post-content {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--color-text, #333);
}

.post-content p {
    margin-bottom: 15px;
    color: #aaa;
    font-size: 15px
}

.post-content a {
    color: var(--color-primary, #007acc);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.3s ease;
}

.post-content a:hover {
    border-bottom-color: var(--color-primary, #007acc);
}

.post-content h1,
.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.3;
}

.post-content h2 {
    font-size: 1.875rem;
    border-bottom: 2px solid var(--color-border, #e5e5e5);
    padding-bottom: 0.5rem;
}

.post-content h3 {
    font-size: 1.5rem;
}

.post-content h4 {
    font-size: 1.25rem;
}

.post-content blockquote {
    border-left: 4px solid var(--color-primary, #007acc);
    margin: 2rem 0;
    padding: 1rem 1.5rem;
    background: var(--color-background-secondary, #f8f9fa);
    font-style: italic;
}

.post-content code {
    background: var(--color-background-secondary, #f8f9fa);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
}

.post-content pre {
    background: var(--color-background-secondary, #f8f9fa);
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.post-content pre code {
    background: none;
    padding: 0;
}

/* Post Author Card */
.post-card-author {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem;
    background: #282828;
    border-radius: 12px;
    margin-top: 3rem;
}

.post-card-author-image {
    margin-bottom: 1rem;
    border-radius: 50%;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.post-card-author-image:hover {
    transform: scale(1.05);
}

.post-card-author-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-text, #333);
    text-decoration: none;
    margin-bottom: 0.5rem;
}

.post-card-author-bio {
    color: var(--color-text-secondary, #666);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.post-card-author-socials {
    display: flex;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Post Sidebar */
.post-sidebar {
    position: sticky;
    top: 2rem;
    height: fit-content;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

.post-sidebar-sticky {
    border: 1px solid #282828;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Table of Contents */
.toc {
    padding: 1.5rem;
}

.toc-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.toc-button:hover {
    background: var(--color-background-secondary, #f8f9fa);
}

.toc-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #fff;
}

.toc-unlock-link {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-border, #e5e5e5);
}

.toc-unlock-link a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #fff;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.toc-unlock-link a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .post-content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-top: 2rem;
    }
    
    .post-sidebar {
        position: static;
        order: -1;
        max-height: none;
        overflow-y: visible;
    }
    
    .post-sidebar-sticky {
        margin-bottom: 2rem;
    }
}

@media (max-width: 768px) {
    .post-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .post-header-info {
        padding-left: 0;
    }
    
    .post-meta {
        padding-left: 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .post-header-buttons {
        width: 100%;
        justify-content: space-between;
    }
    
    .post-content {
        font-size: 1rem;
    }
    
    .post-content h2 {
        font-size: 1.5rem;
    }
    
    .post-content h3 {
        font-size: 1.25rem;
    }
    
    .post-card-author {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .post-featured-image figcaption {
        padding: 15px;
        font-size: 0.75rem;
    }
    
    .post-content {
        font-size: 0.9rem;
    }
    
    .post-content pre {
        padding: 1rem;
        font-size: 0.8rem;
    }
    
    .toc {
         padding: 1rem;
     }
 }

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    color: var(--color-text, #333);
}

.button-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
}

.button-background-200 {
    background: var(--color-background-secondary, #f8f9fa);
    border: 1px solid var(--color-border, #e5e5e5);
}

.button-background-200:hover {
    background: var(--color-background-tertiary, #e9ecef);
    transform: translateY(-1px);
}

.button-icon {
    padding: 0.5rem;
    width: auto;
    height: auto;
}

.button-transparent {
    background: transparent;
    border: 1px solid transparent;
}

.button-transparent:hover {
    background: var(--color-background-secondary, #f8f9fa);
}

.button-rounded {
    border-radius: 50%;
}

/* Dropdown Styles */
.dropdown {
    position: absolute;
    top: 112%;
    right: 0;
    background: white;
    border: 1px solid var(--color-border, #e5e5e5);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-ready {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    display: none;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0.5rem 0;
}

.dropdown li {
    margin: 0;
}

.dropdown .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--color-text, #333);
    text-decoration: none;
    transition: background-color 0.3s ease;
    border: none
}

.dropdown .nav-link:hover {
    background: var(--color-background-secondary, #f8f9fa);
}

.dropdown svg {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

/* Navigation Styles */
.navigation {
    position: relative;
}

.dropdown-toggle {
    position: relative;
}

.dropdown-toggle[aria-expanded="true"] + .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Search Wrapper */
.search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    max-width: 300px;
}

.search-wrapper input {
    width: 100%;
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: 1px solid var(--color-border, #e5e5e5);
    border-radius: 6px;
    font-size: 0.875rem;
    background: white;
    transition: border-color 0.3s ease;
}

.search-wrapper input:focus {
    outline: none;
    border-color: var(--color-primary, #007acc);
    box-shadow: 0 0 0 3px rgba(0, 122, 204, 0.1);
}

.search-wrapper svg {
    position: absolute;
    right: 0.75rem;
    width: 16px;
    height: 16px;
    pointer-events: none;
}

/* Top Bar */
.top-bar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-icon {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.user-icon:hover {
    background: var(--color-background-secondary, #f8f9fa);
}

.user-icon svg {
    width: 20px;
    height: 20px;
}

/* Theme Toggle */
.toggle-color-scheme-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    padding: 0.5rem 1rem;
    border-radius: 99px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-color-scheme-button:hover {
    background: var(--color-background-secondary, #f8f9fa);
}

.toggle-color-scheme-button .label {
    font-size: 0.875rem;
    font-weight: 500;
}

.toggle-color-scheme-button .icon-wrapper {
    display: flex;
    align-items: center;
}

/* Post Title */
.post-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin: 0 0 1rem 0;
    color: var(--color-text, #333);
}

/* Separator */
.separator {
    color: var(--color-text-secondary, #666);
    font-weight: normal;
}

/* Count */
.count {
    background: var(--color-primary, #007acc);
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.25rem;
}

/* Avatar Styles */
.avatar {
    border-radius: 50%;
    overflow: hidden;
    display: inline-block;
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Tooltip Styles */
.tooltip-custom {
    position: absolute;
    background: white;
    border: 1px solid var(--color-border, #e5e5e5);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    z-index: 1000;
    max-width: 250px;
}

.tooltip-avatar {
    display: block;
    margin-bottom: 0.5rem;
}

.tooltip-title {
    font-weight: 600;
    color: var(--color-text, #333);
    text-decoration: none;
    margin-bottom: 0.25rem;
    display: block;
}

.tooltip-icons {
    display: flex;
    gap: 0.5rem;
    list-style: none;
    margin: 0.5rem 0 0 0;
    padding: 0;
}


/* Responsive adjustments for top bar */
@media (max-width: 768px) {
    .top-bar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .top-bar-right {
        justify-content: space-between;
    }
    
    .search-wrapper {
        max-width: none;
        flex: 1;
    }
    
    .post-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .post-title {
        font-size: 1.75rem;
    }
    
    .toggle-color-scheme-button .label {
        display: none;
    }
}



