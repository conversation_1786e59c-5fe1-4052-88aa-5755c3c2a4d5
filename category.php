<?php
/**
 * Category template
 *
 * @package UpCode
 * @since 1.0.0
 */

get_header(); ?>

<body <?php body_class(); ?>>
    <div class="container" role="main">
        <?php get_sidebar(); ?>
        <main class="main" aria-label="<?php esc_attr_e('Category content area', 'upcode'); ?>">
            <?php get_template_part('top-bar'); ?>

            <?php
            // Get current category information
            $current_category = get_queried_object();
            $category_colors = array(
                'selfhosting' => '#ff6900',
                'hacking' => '#4d90fe',
                'cve' => '#ff3e45',
                'cvd' => '#ff3e45',
            );
            $category_color = isset($category_colors[$current_category->slug]) ? $category_colors[$current_category->slug] : '#666';
            ?>

            <!-- Category Header -->
            <div class="category-header">
                <div class="category-info">
                    <h1 class="category-title">
                        <?php echo esc_html($current_category->name); ?>
                        <span style="color: <?php echo esc_attr($category_color); ?>;">●</span>
                    </h1>
                    <?php if ($current_category->description) : ?>
                        <p class="category-description"><?php echo esc_html($current_category->description); ?></p>
                    <?php endif; ?>
                    <div class="category-meta">
                        <span class="post-count">
                            <?php
                            printf(
                                esc_html(_n('%d post', '%d posts', $current_category->count, 'upcode')),
                                number_format_i18n($current_category->count)
                            );
                            ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Category Posts -->
            <?php if (have_posts()) : ?>
                <div class="post-grid category-posts-grid" id="category-posts-grid">
                    <?php while (have_posts()) : the_post();
                        $categories = upcode_get_post_categories_with_colors();
                        $reading_time = upcode_get_reading_time();
                    ?>
                        <article class="post-card" tabindex="0" aria-label="<?php echo esc_attr(sprintf(__('Post: %s', 'upcode'), get_the_title())); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div style="position: relative;">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('upcode-card', array('alt' => get_the_title())); ?>
                                    </a>
                                    <div class="reading-time"><?php echo esc_html($reading_time . ' ' . __('min read', 'upcode')); ?></div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body">
                                <div class="post-author" aria-label="<?php echo esc_attr(sprintf(__('Author %s', 'upcode'), get_the_author())); ?>">
                                    <div class="author-avatar" aria-hidden="true">
                                        <?php echo get_avatar(get_the_author_meta('ID'), 20); ?>
                                    </div>
                                    <?php the_author(); ?>
                                </div>

                                <div class="post-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </div>

                                <div class="post-excerpt">
                                    <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                                </div>

                                <div class="post-info">
                                    <span class="post-date"><?php echo get_the_date(); ?></span>
                                    <?php if (!empty($categories)) : ?>
                                        <?php foreach ($categories as $category) : ?>
                                            <span class="post-category" style="color: <?php echo esc_attr($category['color']); ?>">
                                                <?php echo esc_html($category['name']); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>

                <!-- Pagination -->
                <?php
                global $wp_query;
                if ($wp_query->max_num_pages > 1) :
                ?>
                    <div class="pagination-wrapper">
                        <?php
                        the_posts_pagination(array(
                            'mid_size' => 2,
                            'prev_text' => __('← Previous', 'upcode'),
                            'next_text' => __('Next →', 'upcode'),
                            'before_page_number' => '<span class="screen-reader-text">' . __('Page', 'upcode') . ' </span>',
                        ));
                        ?>
                    </div>

                    <!-- AJAX Load More Button (Alternative to pagination) -->
                    <button class="btn-loadmore" id="load-more-category"
                            data-page="1"
                            data-max-pages="<?php echo $wp_query->max_num_pages; ?>"
                            data-category="<?php echo $current_category->term_id; ?>"
                            style="display: none;"
                            aria-label="<?php esc_attr_e('Load more posts', 'upcode'); ?>">
                        <span class="load-text"><?php esc_html_e('Load More', 'upcode'); ?></span>
                        <span class="loading-text" style="display: none;"><?php esc_html_e('Loading...', 'upcode'); ?></span>
                    </button>
                <?php endif; ?>

            <?php else : ?>
                <!-- No Posts Found -->
                <div class="no-posts-found">
                    <div class="no-posts-icon">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h2><?php esc_html_e('No posts found', 'upcode'); ?></h2>
                    <p><?php echo esc_html(sprintf(__('No posts found in the %s category.', 'upcode'), $current_category->name)); ?></p>
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="btn-home">
                        <?php esc_html_e('Back to Home', 'upcode'); ?>
                    </a>
                </div>
            <?php endif; ?>

        </main>
    </div>

<?php get_footer(); ?>