<?php require('header.php'); ?>
<link rel="stylesheet" href="./single.css" />
<body>
  <div class="container" role="main"> <?php require('sidebar.php'); ?> <main class="main" aria-label="Blog content area"> <?php require('top-bar.php'); ?> <article id="content" class="content post">
        <header class="post-header container-wide">
          <div class="post-header-info">
            <h1 class="post-title">XSS Vulnerability in WeddingShare &lt;= v1.4.7</h1>
            <ul class="post-meta">
              <li>
                <div class="post-meta-author">
                  <a href="/author/lrvt/" class="post-meta-author-image" tabindex="-1" aria-label="LRVT" data-tooltip-custom="author" aria-expanded="false">
                    <picture class="avatar avatar-large avatar-image">
                      <img src="https://blog.lrvt.de/content/images/size/w1200/format/webp/2024/07/19133.png" width="1340" height="530" alt="LRVT" sizes="(max-width: 991.98px) 90vw, 40vw" loading="eager" />
                    </picture>
                  </a> by <a href="https://blog.lrvt.de/author/lrvt/" data-tooltip-custom="author" aria-expanded="false">LRVT</a>
                  <div class="tooltip-custom tooltip-custom-author" style="display: none;">
                    <a href="/author/lrvt/" class="tooltip-avatar" aria-label="LRVT" tabindex="-1">
                      <picture class="avatar avatar-large avatar-image">
                        <img src="https://blog.lrvt.de/content/images/size/w1200/format/webp/2024/07/19133.png" width="1340" height="530" alt="LRVT" sizes="(max-width: 991.98px) 90vw, 40vw" loading="eager" />
                      </picture>
                    </a>
                    <a href="/author/lrvt/" class="tooltip-title">LRVT</a>
                    <div>IT Security Professional and Researcher</div>
                    <ul class="tooltip-icons"></ul>
                  </div>
                </div>
              </li>
              <li class="separator">•</li>
              <li>
                <time class="post-meta-date" datetime="2025-01-14">January 14, 2025</time>
              </li>
              <li class="separator">•</li>
              <li>
                <span class="post-meta-time">1 min read</span>
              </li>
            </ul>
          </div>
          <div class="post-header-buttons">
            <button class="button button-background-200 button-sm post-comments-button" aria-expanded="false" style="display:none">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.4997 9.58333C17.4997 13.4953 14.3283 16.6667 10.4163 16.6667C9.519 16.6667 8.66067 16.4998 7.87065 16.1954C7.72621 16.1398 7.65398 16.112 7.59655 16.0988C7.54006 16.0858 7.49917 16.0803 7.44124 16.0781C7.38234 16.0758 7.31772 16.0825 7.18849 16.0958L2.92097 16.537C2.5141 16.579 2.31067 16.6001 2.19067 16.5268C2.08614 16.4631 2.01495 16.3566 1.996 16.2357C1.97424 16.0968 2.07146 15.9168 2.26588 15.557L3.62893 13.034C3.74118 12.8263 3.79731 12.7223 3.82273 12.6224C3.84783 12.5238 3.85391 12.4527 3.84587 12.3512C3.83774 12.2484 3.79266 12.1147 3.7025 11.8472C3.46289 11.1363 3.33302 10.375 3.33302 9.58333C3.33302 5.67132 6.50433 2.5 10.4163 2.5C14.3283 2.5 17.4997 5.67132 17.4997 9.58333Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
              <span class="label">Comments</span>
              <span class="count"></span>
            </button>
            <div class="post-button-share navigation">
              <button class="button button-background-200 button-sm dropdown-toggle" aria-expanded="false">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17.5 10V13.5C17.5 14.9002 17.5 15.6002 17.2275 16.135C16.9878 16.6054 16.6054 16.9878 16.135 17.2275C15.6002 17.5 14.9002 17.5 13.5 17.5H6.5C5.09987 17.5 4.3998 17.5 3.86503 17.2275C3.39462 16.9878 3.01217 16.6054 2.77248 16.135C2.5 15.6002 2.5 14.9002 2.5 13.5V10M13.3333 5.83333L10 2.5M10 2.5L6.66667 5.83333M10 2.5V12.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <span class="label">Share</span>
              </button>
              <div class="dropdown dropdown-share dropdown-ready">
                <ul>
                  <li>
                    <a class="nav-link" href="https://x.com/intent/tweet?url=https%3A%2F%2Fblog.lrvt.de%2Fxss-vulnerability-in-weddingshare-v1-4-7%2F&amp;text=XSS%20Vulnerability%20in%20WeddingShare%20%3C%3D%20v1.4.7" rel="noopener" target="_blank">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18.2437 2.25H21.5531L14.325 10.5094L22.8281 21.75H16.1719L10.9547 14.9344L4.99216 21.75H1.6781L9.40779 12.9141L1.25623 2.25H8.08123L12.7922 8.47969L18.2437 2.25ZM17.0812 19.7719H18.914L7.08279 4.125H5.11404L17.0812 19.7719Z" fill="currentColor"></path>
                      </svg>
                      <span class="label">Share on X</span>
                    </a>
                  </li>
                  <li>
                    <a class="nav-link" href="https://www.facebook.com/sharer/sharer.php?u=https%3A%2F%2Fblog.lrvt.de%2Fxss-vulnerability-in-weddingshare-v1-4-7%2F&amp;quote=XSS%20Vulnerability%20in%20WeddingShare%20%3C%3D%20v1.4.7" rel="noopener" target="_blank">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M24 12C24 5.37188 18.6281 0 12 0C5.37188 0 0 5.37188 0 12C0 17.625 3.87656 22.35 9.10312 23.6484V15.6656H6.62812V12H9.10312V10.4203C9.10312 6.3375 10.95 4.44375 14.9625 4.44375C15.7219 4.44375 17.0344 4.59375 17.5734 4.74375V8.0625C17.2922 8.03437 16.8 8.01562 16.1859 8.01562C14.2172 8.01562 13.4578 8.76094 13.4578 10.6969V12H17.3766L16.7016 15.6656H13.4531V23.9109C19.3969 23.1938 24 18.1359 24 12Z" fill="currentColor"></path>
                      </svg>
                      <span class="label">Share on Facebook</span>
                    </a>
                  </li>
                  <li>
                    <a class="nav-link" href="https://www.linkedin.com/sharing/share-offsite/?url=https%3A%2F%2Fblog.lrvt.de%2Fxss-vulnerability-in-weddingshare-v1-4-7%2F&amp;summary=XSS%20Vulnerability%20in%20WeddingShare%20%3C%3D%20v1.4.7" rel="noopener" target="_blank">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 1.5H2.99531C2.17031 1.5 1.5 2.17969 1.5 3.01406V20.9859C1.5 21.8203 2.17031 22.5 2.99531 22.5H21C21.825 22.5 22.5 21.8203 22.5 20.9859V3.01406C22.5 2.17969 21.825 1.5 21 1.5ZM7.84687 19.5H4.73438V9.47812H7.85156V19.5H7.84687ZM6.29062 8.10938C5.29219 8.10938 4.48594 7.29844 4.48594 6.30469C4.48594 5.31094 5.29219 4.5 6.29062 4.5C7.28437 4.5 8.09531 5.31094 8.09531 6.30469C8.09531 7.30312 7.28906 8.10938 6.29062 8.10938ZM19.5141 19.5H16.4016V14.625C16.4016 13.4625 16.3781 11.9672 14.7844 11.9672C13.1625 11.9672 12.9141 13.2328 12.9141 14.5406V19.5H9.80156V9.47812H12.7875V10.8469H12.8297C13.2469 10.0594 14.2641 9.22969 15.7781 9.22969C18.9281 9.22969 19.5141 11.3062 19.5141 14.0062V19.5Z" fill="currentColor"></path>
                      </svg>
                      <span class="label">Share on LinkedIn</span>
                    </a>
                  </li>
                  <li>
                    <a class="nav-link" href="https://www.pinterest.com/pin/create/button/?url=https%3A%2F%2Fblog.lrvt.de%2Fxss-vulnerability-in-weddingshare-v1-4-7%2F&amp;description=XSS%20Vulnerability%20in%20WeddingShare%20%3C%3D%20v1.4.7" rel="noopener" target="_blank">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M23.625 12C23.625 18.4218 18.4218 23.625 12 23.625C10.8 23.625 9.64688 23.4422 8.55938 23.1047C9.03282 22.3312 9.74062 21.0655 10.0031 20.0578C10.1437 19.5141 10.725 17.2922 10.725 17.2922C11.1047 18.0141 12.2109 18.6281 13.3875 18.6281C16.8938 18.6281 19.4204 15.4032 19.4204 11.3953C19.4204 7.55625 16.2843 4.68282 12.2531 4.68282C7.2375 4.68282 4.57032 8.04843 4.57032 11.7188C4.57032 13.425 5.4797 15.5484 6.92813 16.2234C7.14845 16.3266 7.26562 16.2796 7.31719 16.0687C7.35469 15.9093 7.55157 15.1172 7.64062 14.7516C7.66875 14.6344 7.65469 14.5313 7.56094 14.4188C7.0875 13.8328 6.70312 12.7641 6.70312 11.7656C6.70312 9.20155 8.64375 6.72187 11.9531 6.72187C14.8078 6.72187 16.8094 8.66719 16.8094 11.4516C16.8094 14.5969 15.2204 16.7766 13.1531 16.7766C12.0141 16.7766 11.1563 15.8344 11.4328 14.6766C11.7609 13.2937 12.3937 11.8031 12.3937 10.8047C12.3937 9.91407 11.9156 9.16875 10.9219 9.16875C9.7547 9.16875 8.81719 10.3734 8.81719 11.9906C8.81719 13.0219 9.16407 13.7156 9.16407 13.7156C9.16407 13.7156 8.01563 18.5813 7.80468 19.4906C7.57032 20.4938 7.66407 21.9093 7.7625 22.8282C3.44063 21.1359 0.375 16.9266 0.375 12C0.375 5.57812 5.57812 0.375 12 0.375C18.4218 0.375 23.625 5.57812 23.625 12Z" fill="currentColor"></path>
                      </svg>
                      <span class="label">Share on Pinterest</span>
                    </a>
                  </li>
                  <li>
                    <a class="nav-link" href="mailto:?subject=XSS%20Vulnerability%20in%20WeddingShare%20%3C%3D%20v1.4.7&amp;body=https%3A%2F%2Fblog.lrvt.de%2Fxss-vulnerability-in-weddingshare-v1-4-7%2F" rel="noopener" target="_blank">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.66667 5.83325L8.47075 10.5961C9.02175 10.9818 9.29725 11.1747 9.59692 11.2493C9.86158 11.3153 10.1384 11.3153 10.4031 11.2493C10.7028 11.1747 10.9783 10.9818 11.5293 10.5961L18.3333 5.83325M5.66667 16.6666H14.3333C15.7335 16.6666 16.4335 16.6666 16.9683 16.3941C17.4388 16.1544 17.8212 15.772 18.0608 15.3016C18.3333 14.7668 18.3333 14.0668 18.3333 12.6666V7.33325C18.3333 5.93312 18.3333 5.23305 18.0608 4.69828C17.8212 4.22787 17.4388 3.84542 16.9683 3.60574C16.4335 3.33325 15.7335 3.33325 14.3333 3.33325H5.66667C4.26653 3.33325 3.56647 3.33325 3.03169 3.60574C2.56128 3.84542 2.17883 4.22787 1.93915 4.69828C1.66667 5.23305 1.66667 5.93312 1.66667 7.33325V12.6666C1.66667 14.0668 1.66667 14.7668 1.93915 15.3016C2.17883 15.772 2.56128 16.1544 3.03169 16.3941C3.56647 16.6666 4.26653 16.6666 5.66667 16.6666Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                      <span class="label">Email</span>
                    </a>
                  </li>
                  <li>
                    <button class="nav-link" data-clipboard-text="https://blog.lrvt.de/xss-vulnerability-in-weddingshare-v1-4-7/">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.33332 10.8332C8.69117 11.3117 9.14775 11.7075 9.67208 11.994C10.1964 12.2805 10.7762 12.4508 11.3722 12.4935C11.9682 12.5362 12.5663 12.4502 13.1261 12.2413C13.6859 12.0326 14.1942 11.7057 14.6167 11.2832L17.1167 8.78325C17.8757 7.99737 18.2956 6.94486 18.2862 5.85238C18.2767 4.7599 17.8384 3.71484 17.0659 2.94231C16.2933 2.16977 15.2483 1.73157 14.1558 1.72208C13.0633 1.71258 12.0108 2.13256 11.225 2.89156L9.79167 4.31656M11.6667 9.16658C11.3087 8.68808 10.8522 8.29223 10.3278 8.00577C9.8035 7.71931 9.22375 7.54896 8.62775 7.50627C8.03182 7.46359 7.43367 7.54958 6.87387 7.7584C6.31407 7.96722 5.80572 8.294 5.38332 8.71658L2.88332 11.2166C2.12433 12.0024 1.70435 13.0549 1.71385 14.1474C1.72334 15.2399 2.16154 16.2849 2.93407 17.0575C3.70661 17.83 4.75167 18.2682 5.84415 18.2777C6.93663 18.2872 7.98914 17.8672 8.775 17.1082L10.2 15.6832" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                      <span class="label">Copy link</span>
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </header>
        <div class="post-featured">
          <figure class="post-featured-image">
            <picture>
              <img src="https://images.unsplash.com/photo-1509518408633-d42f618a2bdc?crop=entropy&amp;cs=tinysrgb&amp;fit=max&amp;fm=jpg&amp;ixid=M3wxMTc3M3wwfDF8c2VhcmNofDV8fHBob3RvfGVufDB8fHx8MTczNjkyOTM0MHww&amp;ixlib=rb-4.0.3&amp;q=80&amp;w=2000" width="1340" height="530" alt="XSS Vulnerability in WeddingShare &lt;= v1.4.7" loading="eager" />
            </picture>
            <figcaption>
              <span style="white-space: pre-wrap;">Photo by </span>
              <a href="https://unsplash.com/@yxvi?utm_source=ghost&amp;utm_medium=referral&amp;utm_campaign=api-credit" target="_blank" rel="noopener noreferrer">
                <span style="white-space: pre-wrap;">Yuvraj Singh</span>
              </a>
              <span style="white-space: pre-wrap;"> / </span>
              <a href="https://unsplash.com/?utm_source=ghost&amp;utm_medium=referral&amp;utm_campaign=api-credit" target="_blank" rel="noopener noreferrer">
                <span style="white-space: pre-wrap;">Unsplash</span>
              </a>
            </figcaption>
          </figure>
        </div>
        <nav class="post-tags" aria-label="Post Tags Navigation">
          <ul>
            <li>
              <a class="post-tag" href="/tag/cve/">CVE / CVD</a>
            </li>
          </ul>
        </nav>
        <div class="post-content-grid">
          <div class="post-content-wrapper">
            <div class="post-content">
              <p>
                WeddingShare is a selfhosted web application, which acts as a place for guests to view and drop pictures of the big day (e.g. your wedding, birthday party and so on).
              </p>
              <p> During testing out WeddingShare, I've noticed a Cross-Site Scripting (XSS) vulnerabity in shared galleries. In detail, WeddingShare will ask guests to provide their name in order to subsequently know the user that uploaded an image. This name is later displayed again without sanitization, when an uploaded picture is inspected. </p>
              <p> Successful exploitation allows for privilege escalation and may allow unauthenticated guests with knowledge of a gallery URL to target the website's administrator or other guests. User interaction is required though, as victims must actively preview an image with an XSS payload. </p>
              <p>The issue was reported to the maintainer and promptly fixed in the new release v1.4.8.</p>
              <p>
                WeddingShare is a selfhosted web application, which acts as a place for guests to view and drop pictures of the big day (e.g. your wedding, birthday party and so on).
              </p>
              <p> During testing out WeddingShare, I've noticed a Cross-Site Scripting (XSS) vulnerabity in shared galleries. In detail, WeddingShare will ask guests to provide their name in order to subsequently know the user that uploaded an image. This name is later displayed again without sanitization, when an uploaded picture is inspected. </p>
              <p> Successful exploitation allows for privilege escalation and may allow unauthenticated guests with knowledge of a gallery URL to target the website's administrator or other guests. User interaction is required though, as victims must actively preview an image with an XSS payload. </p>
              <p>The issue was reported to the maintainer and promptly fixed in the new release v1.4.8.</p>
              <p>WeddingShare is a selfhosted web application, which acts as a place for guests to view and drop pictures of the big day (e.g. your wedding, birthday party and so on).
              </p>
              <p> During testing out WeddingShare, I've noticed a Cross-Site Scripting (XSS) vulnerabity in shared galleries. In detail, WeddingShare will ask guests to provide their name in order to subsequently know the user that uploaded an image. This name is later displayed again without sanitization, when an uploaded picture is inspected. </p>
              <p> Successful exploitation allows for privilege escalation and may allow unauthenticated guests with knowledge of a gallery URL to target the website's administrator or other guests. User interaction is required though, as victims must actively preview an image with an XSS payload. </p>
              <p>The issue was reported to the maintainer and promptly fixed in the new release v1.4.8.</p>
            </div>
            <div class="post-card-author">
              <a href="/author/lrvt/" class="post-card-author-image" aria-label="LRVT" tabindex="-1">
                <picture class="avatar avatar-large avatar-image">
                  <img src="https://blog.lrvt.de/content/images/size/w1200/format/webp/2024/07/19133.png" width="1340" height="530" alt="LRVT" sizes="(max-width: 991.98px) 90vw, 40vw" loading="eager" />
                </picture>
              </a>
              <a href="/author/lrvt/" class="post-card-author-name">LRVT</a>
              <div class="post-card-author-bio">IT Security Professional and Researcher</div>
              <ul class="post-card-author-socials"></ul>
            </div>
          </div>
          <aside class="post-sidebar">
            <div class="post-sidebar-sticky toc-scroll">
              <div class="toc toc-hidden toc-handled">
                <button class="toc-button button button-sm button-icon button-transparent button-rounded" aria-expanded="false" aria-label="Collapse table of contents">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line x1="1.25" y1="4.25" x2="10.75" y2="4.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></line>
                    <line x1="3.25" y1="8.25" x2="12.75" y2="8.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></line>
                    <line x1="5.25" y1="12.25" x2="14.75" y2="12.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></line>
                  </svg>
                </button>
                <div class="toc-title">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line x1="1.25" y1="4.25" x2="10.75" y2="4.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></line>
                    <line x1="3.25" y1="8.25" x2="12.75" y2="8.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></line>
                    <line x1="5.25" y1="12.25" x2="14.75" y2="12.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></line>
                  </svg> On this page
                </div>
                <div class="toc-unlock-link">
                  <ul>
                    <li>
                      <a href="#introduction" class="">Introduction</a>
                    </li>
                    <li>
                      <a href="#the-problemcpe-gap" class="">The Problem - CPE Gap</a>
                    </li>
                    <li>
                      <a href="#deep-dive-into-nvd-database" class="">Deep Dive into NVD Database</a>
                    </li>
                    <li>
                      <a href="#why-this-hurts" class="">Why This Hurts</a>
                      <ul></ul>
                    </li>
                    <li>
                      <a href="#alternative-data-sources" class="">Alternative Data Sources</a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </article>
    </main>
  </div> 
<?php require('footer.php'); ?>
