<?php
/**
 * Top bar template
 *
 * @package UpCode
 * @since 1.0.0
 */
?>
<div class="top-bar top-bar-desktop">
    <div class="search-wrapper">
        <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
            <input type="search" name="s" placeholder="<?php esc_attr_e('Search', 'upcode'); ?>" aria-label="<?php esc_attr_e('Search posts', 'upcode'); ?>" value="<?php echo get_search_query(); ?>" />
            <svg viewBox="0 0 24 24" aria-hidden="true" focusable="false">
                <circle cx="11" cy="11" r="7" stroke="#888" stroke-width="2" fill="none" />
                <line x1="16" y1="16" x2="21" y2="21" stroke="#888" stroke-width="2" />
            </svg>
        </form>
    </div>
    <div class="top-bar-right">
        <?php if (is_user_logged_in()) : ?>
            <a href="<?php echo esc_url(admin_url()); ?>" class="user-icon" aria-label="<?php esc_attr_e('Admin Dashboard', 'upcode'); ?>">
                <?php echo get_avatar(get_current_user_id(), 24); ?>
            </a>
        <?php else : ?>
            <a href="<?php echo esc_url(wp_login_url()); ?>" class="user-icon" aria-label="<?php esc_attr_e('Login', 'upcode'); ?>">
                <svg viewBox="0 0 24 24" aria-hidden="true" focusable="false">
                    <circle cx="12" cy="8" r="4" />
                    <path d="M6 20c0-3 12-3 12 0v1H6v-1z" />
                </svg>
            </a>
        <?php endif; ?>
        <button class="nav-link toggle-color-scheme-button" value="dark-light-system" aria-label="<?php esc_attr_e('Toggle color scheme', 'upcode'); ?>">
            <span class="label label-light"><?php esc_html_e('Theme: Light', 'upcode'); ?></span>
            <span class="label label-dark"><?php esc_html_e('Theme: Dark', 'upcode'); ?></span>
            <span class="icon-wrapper">
                <svg class="icon icon-sun" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.9999 13.3334C11.8409 13.3334 13.3333 11.841 13.3333 10C13.3333 8.15907 11.8409 6.66669 9.9999 6.66669C8.15895 6.66669 6.66656 8.15907 6.66656 10C6.66656 11.841 8.15895 13.3334 9.9999 13.3334Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M9.9999 1.66669V3.33336M9.9999 16.6666V18.3334M4.10824 4.10836L5.28324 5.28336M14.7166 14.7166L15.8916 15.8916M1.66656 10H3.33324M16.6666 10H18.3333M5.28324 14.7166L4.10824 15.8916M15.8916 4.10836L14.7166 5.28336" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <svg class="icon icon-moon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.2957 10.797C17.1482 12.8098 14.9826 14.1668 12.5 14.1668C8.81808 14.1668 5.83333 11.182 5.83333 7.50007C5.83333 5.01727 7.19056 2.85146 9.20358 1.7041C4.97479 2.10506 1.66666 5.66613 1.66666 9.99983C1.66666 14.6023 5.39762 18.3332 10 18.3332C14.3335 18.3332 17.8944 15.0254 18.2957 10.797Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </span>
        </button>
    </div>
</div>
<div class="category-tabs" role="tablist" aria-label="<?php esc_attr_e('Post category tabs', 'upcode'); ?>">
    <a href="<?php echo esc_url(home_url('/')); ?>" role="tab" aria-selected="<?php echo is_home() ? 'true' : 'false'; ?>" class="<?php echo is_home() ? 'active' : ''; ?>">
        <?php esc_html_e('All Posts', 'upcode'); ?>
    </a>
    <?php
    // Get main categories
    $categories = get_categories(array(
        'orderby' => 'count',
        'order' => 'DESC',
        'number' => 3,
        'hide_empty' => true,
    ));

    if ($categories) :
        foreach ($categories as $category) :
            $is_current = is_category($category->term_id);
    ?>
        <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" role="tab" aria-selected="<?php echo $is_current ? 'true' : 'false'; ?>" class="<?php echo $is_current ? 'active' : ''; ?>">
            <?php echo esc_html($category->name); ?>
        </a>
    <?php
        endforeach;
    endif;
    ?>
</div>