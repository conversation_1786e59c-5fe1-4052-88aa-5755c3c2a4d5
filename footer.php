<?php
/**
 * Footer template
 *
 * @package UpCode
 * @since 1.0.0
 */
?>

<!-- Mobile Navigation Popup -->
<div class="popup popup-navigation popup-hide" data-popup-id="popup-navigation" role="dialog" aria-modal="true" aria-hidden="true" aria-label="<?php esc_attr_e('You can hide this popup with the ESC key', 'upcode'); ?>">
    <div class="popup-container">
        <aside class="sidebar" aria-label="<?php esc_attr_e('Sidebar navigation', 'upcode'); ?>">
            <div class="popup-head">
                <button class="popup-close button button-icon button-background-100 button-sm button-rounded" aria-label="<?php esc_attr_e('Hide popup', 'upcode'); ?>">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </button>
                <div class="logo" aria-label="<?php echo esc_attr(get_bloginfo('name')); ?>">
                    <?php if (has_custom_logo()) : ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                            <?php the_custom_logo(); ?>
                        </a>
                    <?php else : ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                            <?php bloginfo('name'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="popup-body">
                <nav>
                    <?php
                    // Display navigation menu for mobile
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_class' => 'mobile-nav',
                        'container' => false,
                        'fallback_cb' => 'upcode_fallback_menu',
                    ));
                    ?>
                </nav>
                <div class="tags" aria-label="<?php esc_attr_e('Tags', 'upcode'); ?>">
                    <h3><?php esc_html_e('Tags', 'upcode'); ?></h3>
                    <div class="tag-list">
                        <?php
                        // Get popular categories for mobile
                        $categories = get_categories(array(
                            'orderby' => 'count',
                            'order' => 'DESC',
                            'number' => 5,
                            'hide_empty' => true,
                        ));

                        if ($categories) :
                            foreach ($categories as $category) :
                                $category_colors = array(
                                    'selfhosting' => 'selfhosting',
                                    'hacking' => 'hacking',
                                    'cve' => 'cve',
                                    'cvd' => 'cve',
                                );
                                $color_class = isset($category_colors[$category->slug]) ? $category_colors[$category->slug] : 'selfhosting';
                                $is_current = (is_category($category->term_id)) ? 'active' : '';
                        ?>
                            <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="<?php echo esc_attr($is_current); ?>" aria-pressed="<?php echo $is_current ? 'true' : 'false'; ?>">
                                <span class="tag-color <?php echo esc_attr($color_class); ?>" aria-hidden="true"></span>
                                <?php echo esc_html($category->name); ?>
                            </a>
                        <?php
                            endforeach;
                        endif;
                        ?>
                    </div>
                </div>
            </div>
            <footer class="sidebar-footer" aria-label="<?php esc_attr_e('Site footer links', 'upcode'); ?>">
                <a href="<?php echo esc_url(get_privacy_policy_url()); ?>"><?php esc_html_e('Privacy', 'upcode'); ?></a>
                <?php if (is_user_logged_in()) : ?>
                    <a href="<?php echo esc_url(admin_url()); ?>"><?php esc_html_e('Admin', 'upcode'); ?></a>
                <?php endif; ?>
            </footer>
        </aside>
    </div>
</div>
<!-- Search Popup -->
<div id="search-popup" class="popup popup-search popup-hide" data-popup-id="popup-search" role="dialog" aria-modal="true" aria-hidden="true" aria-label="<?php esc_attr_e('Search posts', 'upcode'); ?>">
    <div class="popup-container popup-search-container">
        <div class="popup-head">
            <h3><?php esc_html_e('Search Posts', 'upcode'); ?></h3>
            <button class="popup-close button button-icon button-background-100 button-sm button-rounded" aria-label="<?php esc_attr_e('Close search', 'upcode'); ?>">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </button>
        </div>
        <div class="popup-body">
            <div class="search-form">
                <div class="search-input-wrapper">
                    <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                        <input type="search" name="s" id="search-input" placeholder="<?php esc_attr_e('Type to search posts...', 'upcode'); ?>" aria-label="<?php esc_attr_e('Search posts', 'upcode'); ?>" autocomplete="off" value="<?php echo get_search_query(); ?>" />
                    </form>
                    <svg class="search-icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.5 17.5L13.875 13.875M15.8333 9.16667C15.8333 12.8486 12.8486 15.8333 9.16667 15.8333C5.48477 15.8333 2.5 12.8486 2.5 9.16667C2.5 5.48477 5.48477 2.5 9.16667 2.5C12.8486 2.5 15.8333 5.48477 15.8333 9.16667Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </div>
                <div class="search-results" id="search-results">
                    <div class="search-placeholder">
                        <svg width="48" height="48" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17.5 17.5L13.875 13.875M15.8333 9.16667C15.8333 12.8486 12.8486 15.8333 9.16667 15.8333C5.48477 15.8333 2.5 12.8486 2.5 9.16667C2.5 5.48477 5.48477 2.5 9.16667 2.5C12.8486 2.5 15.8333 5.48477 15.8333 9.16667Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                        <p><?php esc_html_e('Start typing to search for posts...', 'upcode'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php wp_footer(); ?>
</body>
</html>