# UpCode Theme - Deployment Checklist

## 🚀 Pre-Deployment Setup

### 📋 Required Steps:

#### 1. **Category ID Setup** ⚠️ QUAN TRỌNG
- [ ] Tìm Category IDs trong WordPress Admin
- [ ] Cập nhật `index.php` dòng ~151: `$selfhosting_cat_id = YOUR_ID;`
- [ ] Cập nhật `index.php` dòng ~223: `$hacking_cat_id = YOUR_ID;`
- [ ] Cập nhật `index.php` dòng ~295: `$cve_cat_id = YOUR_ID;`

#### 2. **Screenshot Creation** ⚠️ CẦN THIẾT
- [ ] Tạo screenshot.png (1200x900px)
- [ ] Chụp homepage với dark theme
- [ ] Hiển thị sidebar và featured post
- [ ] Upload vào theme root directory

#### 3. **Content Preparation**
- [ ] Tạo categories với slugs: selfhosting, hacking, cve
- [ ] Upload featured images cho posts
- [ ] Tạo sticky posts cho featured section
- [ ] Chuẩn bị sample content

## 📁 File Upload Checklist

### ✅ Core Files (Required):
- [ ] `style.css` - Main stylesheet
- [ ] `functions.php` - Theme functions
- [ ] `index.php` - Homepage template
- [ ] `header.php` - Header template
- [ ] `footer.php` - Footer template
- [ ] `sidebar.php` - Sidebar template

### ✅ Template Files:
- [ ] `single.php` - Single post template
- [ ] `page.php` - Page template
- [ ] `category.php` - Category template
- [ ] `search.php` - Search template
- [ ] `404.php` - Error page template
- [ ] `top-bar.php` - Top bar partial

### ✅ Assets:
- [ ] `script.js` - Theme JavaScript
- [ ] `single.css` - Single post styles
- [ ] `screenshot.png` - Theme screenshot

### 📚 Documentation (Optional):
- [ ] `README.md`
- [ ] `CATEGORY-SETUP.md`
- [ ] `TEMPLATE-UPDATES.md`
- [ ] `THEME-REVIEW.md`

## ⚙️ WordPress Configuration

### 1. **Theme Activation**
- [ ] Upload theme folder to `/wp-content/themes/`
- [ ] Activate theme trong WordPress Admin
- [ ] Check for any error messages

### 2. **Navigation Setup**
- [ ] Go to Appearance > Menus
- [ ] Create "Primary Menu" 
- [ ] Create "Sidebar Menu"
- [ ] Assign to theme locations

### 3. **Customizer Settings**
- [ ] Upload custom logo (Appearance > Customize > Site Identity)
- [ ] Set site title và tagline
- [ ] Configure UpCode Theme Options
- [ ] Test dark/light mode toggle

### 4. **Widget Areas**
- [ ] Configure Sidebar widgets
- [ ] Setup Footer widgets
- [ ] Test widget functionality

## 🧪 Testing Checklist

### 📱 Responsive Testing:
- [ ] Desktop (1920px+)
- [ ] Laptop (1366px)
- [ ] Tablet (768px)
- [ ] Mobile (375px)

### 🔧 Functionality Testing:
- [ ] Homepage loads correctly
- [ ] Featured post displays
- [ ] Category sections show posts
- [ ] AJAX Load More works
- [ ] Search functionality
- [ ] Dark/Light mode toggle
- [ ] Mobile navigation
- [ ] Category pages
- [ ] Single post pages
- [ ] Page template
- [ ] 404 error page

### 🎨 Visual Testing:
- [ ] Colors display correctly
- [ ] Typography renders properly
- [ ] Images scale correctly
- [ ] Icons display properly
- [ ] Animations work smoothly

## 🔍 Performance Check

### ⚡ Speed Testing:
- [ ] Page load time < 3 seconds
- [ ] Images optimized
- [ ] CSS/JS minified
- [ ] No console errors

### 🔧 SEO Check:
- [ ] Title tags working
- [ ] Meta descriptions
- [ ] Heading hierarchy (H1, H2, H3)
- [ ] Alt tags on images
- [ ] Schema markup ready

## 🛡️ Security Verification

### 🔒 Security Checks:
- [ ] No PHP errors in logs
- [ ] AJAX nonce verification working
- [ ] User input properly escaped
- [ ] No direct file access
- [ ] WordPress coding standards

## 📊 Browser Compatibility

### 🌐 Test Browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers

## 🚀 Go-Live Steps

### 1. **Final Checks**
- [ ] All content displays correctly
- [ ] No broken links
- [ ] All images loading
- [ ] Forms working (search, comments)
- [ ] AJAX functionality operational

### 2. **Performance Optimization**
- [ ] Enable caching plugin
- [ ] Optimize images
- [ ] Minify CSS/JS (if not already)
- [ ] Setup CDN (optional)

### 3. **Backup & Monitoring**
- [ ] Create full site backup
- [ ] Setup monitoring
- [ ] Test restore process
- [ ] Document any customizations

## 📞 Support & Maintenance

### 🔧 Regular Maintenance:
- [ ] Update WordPress core
- [ ] Update plugins
- [ ] Monitor performance
- [ ] Check for broken links
- [ ] Review analytics

### 📚 Documentation:
- [ ] Document any customizations
- [ ] Keep track of category IDs
- [ ] Note any plugin dependencies
- [ ] Maintain backup schedule

## ✅ Deployment Complete!

**Congratulations!** 🎉 

Your UpCode WordPress theme is now live and ready to use!

### 📋 Post-Launch Tasks:
- [ ] Submit to search engines
- [ ] Setup analytics
- [ ] Configure SEO plugin
- [ ] Create content calendar
- [ ] Monitor user feedback

---

**Theme:** UpCode v1.0.0  
**Deployment Date:** ___________  
**Deployed By:** ___________
