SCREENSHOT REQUIREMENTS FOR UPCODE THEME
========================================

To complete the WordPress theme, you need to add a screenshot.png file.

Requirements:
- File name: screenshot.png
- Dimensions: 1200x900 pixels (4:3 ratio)
- Format: PNG
- File size: Under 1MB

The screenshot should show:
1. The theme's homepage layout
2. Dark theme mode (default)
3. Sidebar navigation visible
4. Featured post section
5. Post grid layout
6. Clean, modern design

This screenshot will be displayed in:
- WordPress admin theme selection
- Theme directory listings
- Theme preview

You can create this screenshot by:
1. Setting up the theme on a WordPress site
2. Adding sample content (posts, featured image, etc.)
3. Taking a screenshot of the homepage
4. Resizing to 1200x900 pixels
5. Saving as screenshot.png in the theme root directory

The screenshot is important for theme presentation and user selection.
