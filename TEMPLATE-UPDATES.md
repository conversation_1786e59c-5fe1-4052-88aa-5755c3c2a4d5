# Cập nhật Template Files - UpCode Theme

## 📋 Tổng quan cập nhật

Đã cập nhật và tạo mới các template files với vòng lặp động và chức năng AJAX:

### ✅ Files đã cập nhật:
- **category.php** - Template cho trang danh mục
- **page.php** - Template cho trang WordPress (mới tạo)
- **functions.php** - Thêm AJAX handlers
- **script.js** - Thêm JavaScript cho AJAX

## 🔄 Category.php - Cập nhật chi tiết

### Tính năng mới:
- **Vòng lặp động**: Tự động hiển thị bài viết theo danh mục hiện tại
- **Category header**: Hiển thị tên, mô tả và số lượng bài viết
- **<PERSON><PERSON><PERSON> sắc tự động**: Dựa trên slug của danh mục
- **Pagination**: <PERSON><PERSON> trang WordPress chuẩn
- **AJAX Load More**: Tùy chọn load more (<PERSON>n mặc định)
- **Responsive design**: Tương thích mobile

### Cấu trúc mới:
```php
// Lấy thông tin danh mục hiện tại
$current_category = get_queried_object();

// Màu sắc theo slug
$category_colors = array(
    'selfhosting' => '#ff6900',
    'hacking' => '#4d90fe', 
    'cve' => '#ff3e45',
    'cvd' => '#ff3e45',
);

// Vòng lặp WordPress chuẩn
if (have_posts()) :
    while (have_posts()) : the_post();
        // Hiển thị bài viết
    endwhile;
endif;
```

### Các section chính:
1. **Category Header**: Tên, mô tả, số bài viết
2. **Posts Grid**: Lưới bài viết với featured image
3. **Pagination**: Phân trang hoặc load more
4. **No Posts**: Thông báo khi không có bài viết

## 📄 Page.php - Template mới

### Tính năng:
- **Page header**: Tiêu đề, excerpt, meta info
- **Featured image**: Hình ảnh đại diện
- **Content body**: Nội dung trang với styling
- **Child pages**: Hiển thị trang con (nếu có)
- **Parent navigation**: Điều hướng trang cha/anh em
- **Comments**: Hỗ trợ bình luận
- **Edit link**: Link chỉnh sửa cho admin

### Cấu trúc:
```php
// Page header với meta info
<header class="page-header">
    <h1 class="page-title"><?php the_title(); ?></h1>
    <div class="page-meta">
        // Ngày cập nhật, tác giả
    </div>
</header>

// Nội dung trang
<div class="page-content-body">
    <?php the_content(); ?>
</div>

// Trang con (nếu có)
<section class="child-pages">
    // Hiển thị các trang con
</section>
```

## 🔧 Functions.php - Thêm chức năng

### AJAX Handlers mới:
```php
// Load more cho category
function upcode_load_more_category_posts()

// CSS cho page template
function upcode_page_styles()
```

### Hooks đã thêm:
- `wp_ajax_upcode_load_more_category`
- `wp_ajax_nopriv_upcode_load_more_category`

## 📱 JavaScript - Script.js

### Chức năng mới:
- **Category AJAX**: Load more cho trang danh mục
- **Smooth scrolling**: Cuộn mượt cho anchor links
- **Error handling**: Xử lý lỗi AJAX

## 🎨 Styling tự động

### Category Colors:
Màu sắc tự động dựa trên slug:
- `selfhosting` → Orange (#ff6900)
- `hacking` → Blue (#4d90fe)
- `cve` → Red (#ff3e45)
- `cvd` → Red (#ff3e45)

### Page Styling:
CSS tự động inject cho page template:
- Responsive design
- Typography scaling
- Grid layouts cho child pages

## 🚀 Cách sử dụng

### 1. Category Pages:
- Tự động hoạt động khi truy cập `/category/category-name/`
- Hiển thị đúng màu sắc theo slug
- Pagination tự động
- Load more có thể bật bằng cách xóa `style="display: none;"`

### 2. Pages:
- Tự động áp dụng cho tất cả WordPress pages
- Hiển thị child pages nếu có
- Navigation tự động cho page hierarchy

### 3. Customization:
```php
// Thay đổi màu category trong functions.php
$category_colors = array(
    'your-slug' => '#your-color',
);

// Thay đổi số bài viết load more
'posts_per_page' => 9, // Trong AJAX handler
```

## ⚙️ Cấu hình

### Bật Load More cho Category:
Trong `category.php`, tìm dòng:
```php
style="display: none;"
```
Xóa để hiển thị nút Load More thay vì pagination.

### Tùy chỉnh Page Layout:
CSS được inject tự động, có thể override trong `style.css`:
```css
.page-content {
    /* Custom styles */
}
```

## 🔍 Debugging

### Kiểm tra AJAX:
1. Mở Browser Console
2. Click Load More
3. Xem Network tab cho AJAX requests
4. Check for errors in Console

### Kiểm tra Category:
1. Đảm bảo category có bài viết
2. Verify slug đúng cho màu sắc
3. Check featured images

## 📋 Checklist sau cập nhật

- [ ] Test category pages hiển thị đúng
- [ ] Kiểm tra màu sắc category
- [ ] Test pagination/load more
- [ ] Verify page template hoạt động
- [ ] Check child pages display
- [ ] Test responsive design
- [ ] Verify AJAX functionality

## 🆘 Troubleshooting

### Lỗi thường gặp:

1. **AJAX không hoạt động**:
   - Check `upcode_ajax` object trong console
   - Verify nonce
   - Check AJAX URL

2. **Màu sắc không đúng**:
   - Kiểm tra slug của category
   - Update `$category_colors` array

3. **Page styling lỗi**:
   - Clear cache
   - Check CSS conflicts

4. **Child pages không hiển thị**:
   - Verify page hierarchy
   - Check parent-child relationships

---

**Theme đã được cập nhật hoàn chỉnh với vòng lặp động! 🎉**
