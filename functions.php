<?php
/**
 * UpCode Theme Functions
 * 
 * @package UpCode
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme setup
 */
function upcode_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add support for custom header
    add_theme_support('custom-header', array(
        'default-image' => '',
        'width' => 1200,
        'height' => 300,
        'flex-height' => true,
        'flex-width' => true,
    ));
    
    // Add support for custom background
    add_theme_support('custom-background', array(
        'default-color' => '121212',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'upcode'),
        'sidebar' => __('Sidebar Menu', 'upcode'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 800;
    }
    
    // Disable Gutenberg editor (use classic editor)
    add_filter('use_block_editor_for_post', '__return_false', 10);
    add_filter('use_block_editor_for_page', '__return_false', 10);
}
add_action('after_setup_theme', 'upcode_setup');

/**
 * Enqueue scripts and styles
 */
function upcode_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('upcode-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Enqueue main script
    wp_enqueue_script('upcode-script', get_template_directory_uri() . '/script.js', array(), '1.0.0', true);
    
    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'upcode_scripts');

/**
 * Register widget areas
 */
function upcode_widgets_init() {
    register_sidebar(array(
        'name' => __('Sidebar', 'upcode'),
        'id' => 'sidebar-1',
        'description' => __('Add widgets here to appear in your sidebar.', 'upcode'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));
    
    register_sidebar(array(
        'name' => __('Footer', 'upcode'),
        'id' => 'footer-1',
        'description' => __('Add widgets here to appear in your footer.', 'upcode'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="widget-title">',
        'after_title' => '</h3>',
    ));
}
add_action('widgets_init', 'upcode_widgets_init');

/**
 * Custom excerpt length
 */
function upcode_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'upcode_excerpt_length');

/**
 * Custom excerpt more
 */
function upcode_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'upcode_excerpt_more');

/**
 * Get reading time for posts
 */
function upcode_get_reading_time($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $content = get_post_field('post_content', $post_id);
    $word_count = str_word_count(strip_tags($content));
    $reading_time = ceil($word_count / 200); // Average reading speed: 200 words per minute
    
    return $reading_time;
}

/**
 * Get post categories with colors
 */
function upcode_get_post_categories_with_colors($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $categories = get_the_category($post_id);
    $category_colors = array(
        'selfhosting' => '#ff6900',
        'hacking' => '#4d90fe',
        'cve' => '#ff3e45',
        'cvd' => '#ff3e45',
    );
    
    $result = array();
    foreach ($categories as $category) {
        $slug = $category->slug;
        $color = isset($category_colors[$slug]) ? $category_colors[$slug] : '#666';
        $result[] = array(
            'name' => $category->name,
            'slug' => $slug,
            'color' => $color,
            'link' => get_category_link($category->term_id)
        );
    }
    
    return $result;
}

/**
 * Custom post navigation
 */
function upcode_post_navigation() {
    $prev_post = get_previous_post();
    $next_post = get_next_post();
    
    if ($prev_post || $next_post) {
        echo '<nav class="post-navigation" aria-label="' . esc_attr__('Post navigation', 'upcode') . '">';
        
        if ($prev_post) {
            echo '<div class="nav-previous">';
            echo '<a href="' . esc_url(get_permalink($prev_post)) . '" rel="prev">';
            echo '<span class="nav-subtitle">' . esc_html__('Previous Post', 'upcode') . '</span>';
            echo '<span class="nav-title">' . esc_html(get_the_title($prev_post)) . '</span>';
            echo '</a>';
            echo '</div>';
        }
        
        if ($next_post) {
            echo '<div class="nav-next">';
            echo '<a href="' . esc_url(get_permalink($next_post)) . '" rel="next">';
            echo '<span class="nav-subtitle">' . esc_html__('Next Post', 'upcode') . '</span>';
            echo '<span class="nav-title">' . esc_html(get_the_title($next_post)) . '</span>';
            echo '</a>';
            echo '</div>';
        }
        
        echo '</nav>';
    }
}

/**
 * Disable Gutenberg for all post types
 */
function upcode_disable_gutenberg($current_status, $post_type) {
    return false;
}
add_filter('use_block_editor_for_post_type', 'upcode_disable_gutenberg', 10, 2);

/**
 * Add custom body classes
 */
function upcode_body_classes($classes) {
    // Add dark theme class by default
    $classes[] = 'dark-theme';
    
    // Add page-specific classes
    if (is_home() || is_front_page()) {
        $classes[] = 'page-home';
    }
    
    if (is_single()) {
        $classes[] = 'page-single';
    }
    
    if (is_category()) {
        $classes[] = 'page-category';
    }
    
    return $classes;
}
add_filter('body_class', 'upcode_body_classes');

/**
 * Customize login page
 */
function upcode_login_logo() {
    $custom_logo_id = get_theme_mod('custom_logo');
    if ($custom_logo_id) {
        $logo = wp_get_attachment_image_src($custom_logo_id, 'full');
        echo '<style type="text/css">
            #login h1 a, .login h1 a {
                background-image: url(' . esc_url($logo[0]) . ');
                height: 80px;
                width: 320px;
                background-size: contain;
                background-repeat: no-repeat;
                padding-bottom: 30px;
            }
        </style>';
    }
}
add_action('login_enqueue_scripts', 'upcode_login_logo');

/**
 * Load text domain for translations
 */
function upcode_load_textdomain() {
    load_theme_textdomain('upcode', get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'upcode_load_textdomain');

/**
 * Fallback menu for navigation
 */
function upcode_fallback_menu() {
    echo '<ul class="fallback-nav">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">' . esc_html__('Home', 'upcode') . '</a></li>';
    if (is_user_logged_in()) {
        echo '<li><a href="' . esc_url(admin_url()) . '">' . esc_html__('Admin', 'upcode') . '</a></li>';
        echo '<li><a href="' . esc_url(wp_logout_url()) . '">' . esc_html__('Logout', 'upcode') . '</a></li>';
    } else {
        echo '<li><a href="' . esc_url(wp_login_url()) . '">' . esc_html__('Login', 'upcode') . '</a></li>';
    }
    echo '</ul>';
}

/**
 * Add custom CSS for single post styling
 */
function upcode_single_post_styles() {
    if (is_single()) {
        wp_enqueue_style('upcode-single', get_template_directory_uri() . '/single.css', array('upcode-style'), '1.0.0');
    }
}
add_action('wp_enqueue_scripts', 'upcode_single_post_styles');

/**
 * Customize the excerpt more link
 */
function upcode_excerpt_more_link($more) {
    global $post;
    return '<a class="read-more-link" href="' . esc_url(get_permalink($post->ID)) . '">' . esc_html__('Read More', 'upcode') . '</a>';
}
add_filter('excerpt_more', 'upcode_excerpt_more_link');

/**
 * Add custom image sizes
 */
function upcode_custom_image_sizes() {
    add_image_size('upcode-featured', 800, 400, true);
    add_image_size('upcode-card', 400, 225, true);
    add_image_size('upcode-avatar', 64, 64, true);
}
add_action('after_setup_theme', 'upcode_custom_image_sizes');

/**
 * Enqueue admin styles
 */
function upcode_admin_styles() {
    wp_enqueue_style('upcode-admin', get_template_directory_uri() . '/admin.css', array(), '1.0.0');
}
add_action('admin_enqueue_scripts', 'upcode_admin_styles');

/**
 * Add theme customizer options
 */
function upcode_customize_register($wp_customize) {
    // Add section for theme options
    $wp_customize->add_section('upcode_theme_options', array(
        'title' => __('UpCode Theme Options', 'upcode'),
        'priority' => 30,
    ));

    // Add setting for featured posts
    $wp_customize->add_setting('upcode_featured_posts', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));

    $wp_customize->add_control('upcode_featured_posts', array(
        'label' => __('Show Featured Posts', 'upcode'),
        'section' => 'upcode_theme_options',
        'type' => 'checkbox',
    ));

    // Add setting for dark mode default
    $wp_customize->add_setting('upcode_dark_mode_default', array(
        'default' => true,
        'sanitize_callback' => 'wp_validate_boolean',
    ));

    $wp_customize->add_control('upcode_dark_mode_default', array(
        'label' => __('Dark Mode by Default', 'upcode'),
        'section' => 'upcode_theme_options',
        'type' => 'checkbox',
    ));
}
add_action('customize_register', 'upcode_customize_register');

/**
 * AJAX Load More Posts
 */
function upcode_load_more_posts() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'upcode_load_more_nonce')) {
        wp_die('Security check failed');
    }

    $page = intval($_POST['page']);
    $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

    $args = array(
        'posts_per_page' => 6,
        'paged' => $page,
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => '_thumbnail_id',
                'compare' => 'EXISTS'
            )
        )
    );

    // Add category filter if specified
    if ($category_id > 0) {
        $args['cat'] = $category_id;
    }

    $posts_query = new WP_Query($args);

    if ($posts_query->have_posts()) :
        while ($posts_query->have_posts()) : $posts_query->the_post();
            $categories = upcode_get_post_categories_with_colors();
            $reading_time = upcode_get_reading_time();
        ?>
            <article class="post-card" tabindex="0" aria-label="<?php echo esc_attr(sprintf(__('Post: %s', 'upcode'), get_the_title())); ?>">
                <?php if (has_post_thumbnail()) : ?>
                    <div style="position: relative;">
                        <a href="<?php the_permalink(); ?>">
                            <?php the_post_thumbnail('upcode-card', array('alt' => get_the_title())); ?>
                        </a>
                        <div class="reading-time"><?php echo esc_html($reading_time . ' ' . __('min read', 'upcode')); ?></div>
                    </div>
                <?php endif; ?>

                <div class="card-body">
                    <div class="post-author" aria-label="<?php echo esc_attr(sprintf(__('Author %s', 'upcode'), get_the_author())); ?>">
                        <div class="author-avatar" aria-hidden="true">
                            <?php echo get_avatar(get_the_author_meta('ID'), 20); ?>
                        </div>
                        <?php the_author(); ?>
                    </div>

                    <div class="post-title">
                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                    </div>

                    <div class="post-info">
                        <span class="post-date"><?php echo get_the_date(); ?></span>
                        <?php if (!empty($categories)) : ?>
                            <?php foreach ($categories as $category) : ?>
                                <span class="post-category" style="color: <?php echo esc_attr($category['color']); ?>">
                                    <?php echo esc_html($category['name']); ?>
                                </span>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </article>
        <?php
        endwhile;
        wp_reset_postdata();
    endif;

    wp_die(); // Required for AJAX
}
add_action('wp_ajax_upcode_load_more', 'upcode_load_more_posts');
add_action('wp_ajax_nopriv_upcode_load_more', 'upcode_load_more_posts');

/**
 * Localize script for AJAX
 */
function upcode_localize_scripts() {
    wp_localize_script('upcode-script', 'upcode_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('upcode_load_more_nonce'),
        'loading_text' => __('Loading...', 'upcode'),
        'load_more_text' => __('Load More', 'upcode'),
        'no_more_posts' => __('No more posts to load', 'upcode'),
    ));
}
add_action('wp_enqueue_scripts', 'upcode_localize_scripts');
