<?php
/**
 * Main template file
 *
 * @package UpCode
 * @since 1.0.0
 */

get_header(); ?>

<body <?php body_class(); ?>>
    <div class="container" role="main">
        <?php get_sidebar(); ?>
        <main class="main" aria-label="<?php esc_attr_e('Blog content area', 'upcode'); ?>">
            <?php get_template_part('top-bar'); ?>

            <!-- Featured post -->
            <?php
            // Get the latest sticky post or latest post
            $featured_query = new WP_Query(array(
                'posts_per_page' => 1,
                'meta_key' => '_thumbnail_id',
                'post__in' => get_option('sticky_posts'),
                'ignore_sticky_posts' => 1
            ));

            if (!$featured_query->have_posts()) {
                $featured_query = new WP_Query(array(
                    'posts_per_page' => 1,
                    'meta_key' => '_thumbnail_id'
                ));
            }

            if ($featured_query->have_posts()) :
                while ($featured_query->have_posts()) : $featured_query->the_post();
                    $categories = upcode_get_post_categories_with_colors();
                    $reading_time = upcode_get_reading_time();
            ?>
            <article class="featured-post" aria-label="<?php esc_attr_e('Featured post', 'upcode'); ?>">
                <div class="featured-content">
                    <div class="post-author" aria-label="<?php esc_attr_e('Post author', 'upcode'); ?>">
                        <div class="author-avatar" aria-hidden="true">
                            <?php echo get_avatar(get_the_author_meta('ID'), 32); ?>
                        </div>
                        <?php the_author(); ?>
                    </div>
                    <?php if (!empty($categories)) : ?>
                        <div class="category" aria-label="<?php esc_attr_e('Post category', 'upcode'); ?>" style="color: <?php echo esc_attr($categories[0]['color']); ?>">
                            <?php echo esc_html($categories[0]['name']); ?>
                        </div>
                    <?php endif; ?>
                    <h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                    <p class="desc"><?php echo wp_trim_words(get_the_excerpt(), 20, '...'); ?></p>
                    <a href="<?php the_permalink(); ?>" class="btn-readmore" aria-label="<?php echo esc_attr(sprintf(__('Read more about %s', 'upcode'), get_the_title())); ?>">
                        <?php esc_html_e('Read More', 'upcode'); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M5 12h14M13 18l6-6-6-6" />
                        </svg>
                    </a>
                    <div class="featured-nav" aria-label="<?php esc_attr_e('Featured post navigation', 'upcode'); ?>">
                        <svg viewBox="0 0 24 24" role="img" aria-hidden="true">
                            <path d="M15 18l-6-6 6-6" />
                        </svg>
                        <svg viewBox="0 0 24 24" role="img" aria-hidden="true">
                            <path d="M9 6l6 6-6 6" />
                        </svg>
                    </div>
                </div>
                <?php if (has_post_thumbnail()) : ?>
                <div class="featured-image" aria-hidden="true">
                    <?php the_post_thumbnail('large', array('alt' => '')); ?>
                    <div class="reading-time"><?php echo esc_html($reading_time . ' ' . __('min read', 'upcode')); ?></div>
                </div>
                <?php endif; ?>
            </article>
            <?php
                endwhile;
                wp_reset_postdata();
            endif;
            ?>

            <!-- Recent Posts Grid -->
            <div class="post-grid" id="recent-posts-grid" aria-label="<?php esc_attr_e('Recent posts grid', 'upcode'); ?>">
                <?php
                // Query for recent posts (excluding featured post)
                $recent_posts = new WP_Query(array(
                    'posts_per_page' => 6,
                    'post_status' => 'publish',
                    'meta_query' => array(
                        array(
                            'key' => '_thumbnail_id',
                            'compare' => 'EXISTS'
                        )
                    )
                ));

                if ($recent_posts->have_posts()) :
                    while ($recent_posts->have_posts()) : $recent_posts->the_post();
                        $categories = upcode_get_post_categories_with_colors();
                        $reading_time = upcode_get_reading_time();
                ?>
                    <article class="post-card" tabindex="0" aria-label="<?php echo esc_attr(sprintf(__('Post: %s', 'upcode'), get_the_title())); ?>">
                        <?php if (has_post_thumbnail()) : ?>
                            <div style="position: relative;">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('upcode-card', array('alt' => get_the_title())); ?>
                                </a>
                                <div class="reading-time"><?php echo esc_html($reading_time . ' ' . __('min read', 'upcode')); ?></div>
                            </div>
                        <?php endif; ?>

                        <div class="card-body">
                            <div class="post-author" aria-label="<?php echo esc_attr(sprintf(__('Author %s', 'upcode'), get_the_author())); ?>">
                                <div class="author-avatar" aria-hidden="true">
                                    <?php echo get_avatar(get_the_author_meta('ID'), 20); ?>
                                </div>
                                <?php the_author(); ?>
                            </div>

                            <div class="post-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </div>

                            <div class="post-info">
                                <span class="post-date"><?php echo get_the_date(); ?></span>
                                <?php if (!empty($categories)) : ?>
                                    <?php foreach ($categories as $category) : ?>
                                        <span class="post-category" style="color: <?php echo esc_attr($category['color']); ?>">
                                            <?php echo esc_html($category['name']); ?>
                                        </span>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </article>
                <?php
                    endwhile;
                    wp_reset_postdata();
                endif;
                ?>
            </div>

            <!-- Load More Button -->
            <button class="btn-loadmore" id="load-more-posts" data-page="1" data-max-pages="<?php echo $recent_posts->max_num_pages; ?>" aria-label="<?php esc_attr_e('Load more posts', 'upcode'); ?>">
                <span class="load-text"><?php esc_html_e('Load More', 'upcode'); ?></span>
                <span class="loading-text" style="display: none;"><?php esc_html_e('Loading...', 'upcode'); ?></span>
            </button>
            <?php
            // Section: Selfhosting - Category ID: 1 (thay đổi ID này theo danh mục của bạn)
            $selfhosting_cat_id = 1; // Thay đổi ID này
            $selfhosting_category = get_category($selfhosting_cat_id);
            if ($selfhosting_category && !is_wp_error($selfhosting_category)) :
                $selfhosting_posts = new WP_Query(array(
                    'cat' => $selfhosting_cat_id,
                    'posts_per_page' => 3,
                    'post_status' => 'publish',
                    'meta_query' => array(
                        array(
                            'key' => '_thumbnail_id',
                            'compare' => 'EXISTS'
                        )
                    )
                ));

                if ($selfhosting_posts->have_posts()) :
            ?>
            <section aria-label="<?php echo esc_attr($selfhosting_category->name . ' posts'); ?>">
                <div class="section-header">
                    <h2><?php echo esc_html($selfhosting_category->name); ?> <span style="color:#ff6900;">●</span></h2>
                    <a href="<?php echo esc_url(get_category_link($selfhosting_cat_id)); ?>" class="view-more-btn" aria-label="<?php echo esc_attr(sprintf(__('View more %s posts', 'upcode'), $selfhosting_category->name)); ?>">
                        <?php echo esc_html(sprintf(__('View %s', 'upcode'), $selfhosting_category->name)); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M9 6l6 6-6 6" />
                        </svg>
                    </a>
                </div>
                <p class="description"><?php echo esc_html($selfhosting_category->description ?: __('Selfhosting, Homelabs, DevOps, Docker, K8s and more.', 'upcode')); ?></p>
                <div class="post-grid" id="selfhosting-posts-grid">
                    <?php while ($selfhosting_posts->have_posts()) : $selfhosting_posts->the_post();
                        $categories = upcode_get_post_categories_with_colors();
                        $reading_time = upcode_get_reading_time();
                    ?>
                        <article class="post-card" tabindex="0" aria-label="<?php echo esc_attr(sprintf(__('Post: %s', 'upcode'), get_the_title())); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div style="position: relative;">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('upcode-card', array('alt' => get_the_title())); ?>
                                    </a>
                                    <div class="reading-time"><?php echo esc_html($reading_time . ' ' . __('min read', 'upcode')); ?></div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body">
                                <div class="post-author" aria-label="<?php echo esc_attr(sprintf(__('Author %s', 'upcode'), get_the_author())); ?>">
                                    <div class="author-avatar" aria-hidden="true">
                                        <?php echo get_avatar(get_the_author_meta('ID'), 20); ?>
                                    </div>
                                    <?php the_author(); ?>
                                </div>

                                <div class="post-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </div>

                                <div class="post-info">
                                    <span class="post-date"><?php echo get_the_date(); ?></span>
                                    <span class="post-category"><?php echo esc_html($selfhosting_category->name); ?></span>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>
            </section>
            <?php
                endif;
                wp_reset_postdata();
            endif;
            ?>

            <?php
            // Section: Hacking - Category ID: 2 (thay đổi ID này theo danh mục của bạn)
            $hacking_cat_id = 2; // Thay đổi ID này
            $hacking_category = get_category($hacking_cat_id);
            if ($hacking_category && !is_wp_error($hacking_category)) :
                $hacking_posts = new WP_Query(array(
                    'cat' => $hacking_cat_id,
                    'posts_per_page' => 3,
                    'post_status' => 'publish',
                    'meta_query' => array(
                        array(
                            'key' => '_thumbnail_id',
                            'compare' => 'EXISTS'
                        )
                    )
                ));

                if ($hacking_posts->have_posts()) :
            ?>
            <section aria-label="<?php echo esc_attr($hacking_category->name . ' posts'); ?>">
                <div class="section-header">
                    <h2><?php echo esc_html($hacking_category->name); ?> <span style="color:#4d90fe;">●</span></h2>
                    <a href="<?php echo esc_url(get_category_link($hacking_cat_id)); ?>" class="view-more-btn" aria-label="<?php echo esc_attr(sprintf(__('View more %s posts', 'upcode'), $hacking_category->name)); ?>">
                        <?php echo esc_html(sprintf(__('View %s', 'upcode'), $hacking_category->name)); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M9 6l6 6-6 6" />
                        </svg>
                    </a>
                </div>
                <p class="description"><?php echo esc_html($hacking_category->description ?: __('IT Security, Hacking, Coding, Bugs and more.', 'upcode')); ?></p>
                <div class="post-grid" id="hacking-posts-grid">
                    <?php while ($hacking_posts->have_posts()) : $hacking_posts->the_post();
                        $categories = upcode_get_post_categories_with_colors();
                        $reading_time = upcode_get_reading_time();
                    ?>
                        <article class="post-card" tabindex="0" aria-label="<?php echo esc_attr(sprintf(__('Post: %s', 'upcode'), get_the_title())); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div style="position: relative;">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('upcode-card', array('alt' => get_the_title())); ?>
                                    </a>
                                    <div class="reading-time"><?php echo esc_html($reading_time . ' ' . __('min read', 'upcode')); ?></div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body">
                                <div class="post-author" aria-label="<?php echo esc_attr(sprintf(__('Author %s', 'upcode'), get_the_author())); ?>">
                                    <div class="author-avatar" aria-hidden="true">
                                        <?php echo get_avatar(get_the_author_meta('ID'), 20); ?>
                                    </div>
                                    <?php the_author(); ?>
                                </div>

                                <div class="post-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </div>

                                <div class="post-info">
                                    <span class="post-date"><?php echo get_the_date(); ?></span>
                                    <span class="post-category"><?php echo esc_html($hacking_category->name); ?></span>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>
            </section>
            <?php
                endif;
                wp_reset_postdata();
            endif;
            ?>

            <?php
            // Section: CVE / CVD - Category ID: 3 (thay đổi ID này theo danh mục của bạn)
            $cve_cat_id = 3; // Thay đổi ID này
            $cve_category = get_category($cve_cat_id);
            if ($cve_category && !is_wp_error($cve_category)) :
                $cve_posts = new WP_Query(array(
                    'cat' => $cve_cat_id,
                    'posts_per_page' => 3,
                    'post_status' => 'publish',
                    'meta_query' => array(
                        array(
                            'key' => '_thumbnail_id',
                            'compare' => 'EXISTS'
                        )
                    )
                ));

                if ($cve_posts->have_posts()) :
            ?>
            <section aria-label="<?php echo esc_attr($cve_category->name . ' posts'); ?>">
                <div class="section-header">
                    <h2><?php echo esc_html($cve_category->name); ?> <span style="color:#ff3e45;">●</span></h2>
                    <a href="<?php echo esc_url(get_category_link($cve_cat_id)); ?>" class="view-more-btn" aria-label="<?php echo esc_attr(sprintf(__('View more %s posts', 'upcode'), $cve_category->name)); ?>">
                        <?php echo esc_html(sprintf(__('View %s', 'upcode'), $cve_category->name)); ?>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                            <path d="M9 6l6 6-6 6" />
                        </svg>
                    </a>
                </div>
                <p class="description"><?php echo esc_html($cve_category->description ?: __('Vulnerabilities and Disclosures', 'upcode')); ?></p>
                <div class="post-grid" id="cve-posts-grid">
                    <?php while ($cve_posts->have_posts()) : $cve_posts->the_post();
                        $categories = upcode_get_post_categories_with_colors();
                        $reading_time = upcode_get_reading_time();
                    ?>
                        <article class="post-card" tabindex="0" aria-label="<?php echo esc_attr(sprintf(__('Post: %s', 'upcode'), get_the_title())); ?>">
                            <?php if (has_post_thumbnail()) : ?>
                                <div style="position: relative;">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('upcode-card', array('alt' => get_the_title())); ?>
                                    </a>
                                    <div class="reading-time"><?php echo esc_html($reading_time . ' ' . __('min read', 'upcode')); ?></div>
                                </div>
                            <?php endif; ?>

                            <div class="card-body">
                                <div class="post-author" aria-label="<?php echo esc_attr(sprintf(__('Author %s', 'upcode'), get_the_author())); ?>">
                                    <div class="author-avatar" aria-hidden="true">
                                        <?php echo get_avatar(get_the_author_meta('ID'), 20); ?>
                                    </div>
                                    <?php the_author(); ?>
                                </div>

                                <div class="post-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </div>

                                <div class="post-info">
                                    <span class="post-date"><?php echo get_the_date(); ?></span>
                                    <span class="post-category"><?php echo esc_html($cve_category->name); ?></span>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>
            </section>
            <?php
                endif;
                wp_reset_postdata();
            endif;
            ?>
        </main>
    </div>

<?php get_footer(); ?>