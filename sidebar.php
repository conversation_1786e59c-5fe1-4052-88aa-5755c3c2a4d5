<?php
/**
 * Sidebar template
 *
 * @package UpCode
 * @since 1.0.0
 */
?>
<aside class="sidebar" aria-label="<?php esc_attr_e('Sidebar navigation', 'upcode'); ?>">
    <div class="navbar">
        <div class="header-menu">
            <button class="toggle-sidebar button button-icon button-transparent button-rounded" aria-label="<?php esc_attr_e('Collapse Sidebar', 'upcode'); ?>" aria-expanded="false">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.27271 10H15.7272M4.27271 4H15.7272M4.27271 16H15.7272" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </button>
            <div class="logo" aria-label="<?php echo esc_attr(get_bloginfo('name')); ?>">
                <?php if (has_custom_logo()) : ?>
                    <?php the_custom_logo(); ?>
                <?php else : ?>
                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                        <?php bloginfo('name'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <div class="header-menu-right">
            <button class="search-toggle">
                <svg class="icon icon-search" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.5 17.5L13.875 13.875M15.8333 9.16667C15.8333 12.8486 12.8486 15.8333 9.16667 15.8333C5.48477 15.8333 2.5 12.8486 2.5 9.16667C2.5 5.48477 5.48477 2.5 9.16667 2.5C12.8486 2.5 15.8333 5.48477 15.8333 9.16667Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </button>

            <?php if (is_user_logged_in()) : ?>
                <a href="<?php echo esc_url(admin_url()); ?>" class="dropdown-toggle user-login" aria-label="<?php esc_attr_e('Admin Dashboard', 'upcode'); ?>">
                    <span class="avatar">
                        <?php echo get_avatar(get_current_user_id(), 20); ?>
                    </span>
                </a>
            <?php else : ?>
                <a href="<?php echo esc_url(wp_login_url()); ?>" class="dropdown-toggle user-login" aria-label="<?php esc_attr_e('Login', 'upcode'); ?>">
                    <span class="avatar">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2.5 16.6667C4.44649 14.6022 7.08918 13.3333 10 13.3333C12.9108 13.3333 15.5535 14.6022 17.5 16.6667M13.75 6.25C13.75 8.32107 12.0711 10 10 10C7.92893 10 6.25 8.32107 6.25 6.25C6.25 4.17893 7.92893 2.5 10 2.5C12.0711 2.5 13.75 4.17893 13.75 6.25Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                    </span>
                </a>
            <?php endif; ?>

            <button class="nav-link toggle-color-scheme-button" value="dark-light-system" aria-label="<?php esc_attr_e('Toggle color scheme', 'upcode'); ?>">
                <svg class="icon icon-moon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.2957 10.797C17.1482 12.8098 14.9826 14.1668 12.5 14.1668C8.81808 14.1668 5.83333 11.182 5.83333 7.50007C5.83333 5.01727 7.19056 2.85146 9.20358 1.7041C4.97479 2.10506 1.66666 5.66613 1.66666 9.99983C1.66666 14.6023 5.39762 18.3332 10 18.3332C14.3335 18.3332 17.8944 15.0254 18.2957 10.797Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
                <span class="label label-light"><?php esc_html_e('Theme: Light', 'upcode'); ?></span>
                <span class="label label-dark"><?php esc_html_e('Theme: Dark', 'upcode'); ?></span>
                <span class="icon-wrapper">
                    <svg class="icon icon-sun" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9.9999 13.3334C11.8409 13.3334 13.3333 11.841 13.3333 10C13.3333 8.15907 11.8409 6.66669 9.9999 6.66669C8.15895 6.66669 6.66656 8.15907 6.66656 10C6.66656 11.841 8.15895 13.3334 9.9999 13.3334Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M9.9999 1.66669V3.33336M9.9999 16.6666V18.3334M4.10824 4.10836L5.28324 5.28336M14.7166 14.7166L15.8916 15.8916M1.66656 10H3.33324M16.6666 10H18.3333M5.28324 14.7166L4.10824 15.8916M15.8916 4.10836L14.7166 5.28336" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                    <svg class="icon icon-moon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18.2957 10.797C17.1482 12.8098 14.9826 14.1668 12.5 14.1668C8.81808 14.1668 5.83333 11.182 5.83333 7.50007C5.83333 5.01727 7.19056 2.85146 9.20358 1.7041C4.97479 2.10506 1.66666 5.66613 1.66666 9.99983C1.66666 14.6023 5.39762 18.3332 10 18.3332C14.3335 18.3332 17.8944 15.0254 18.2957 10.797Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </span>
            </button>
        </div>
    </div>
    <div class="sidebar-body">
        <nav>
            <?php
            // Display navigation menu
            wp_nav_menu(array(
                'theme_location' => 'sidebar',
                'menu_class' => 'sidebar-nav',
                'container' => false,
                'fallback_cb' => 'upcode_fallback_menu',
            ));
            ?>
        </nav>
        <div class="tags" aria-label="<?php esc_attr_e('Tags', 'upcode'); ?>">
            <h3><?php esc_html_e('Tags', 'upcode'); ?></h3>
            <div class="tag-list">
                <?php
                // Get popular categories
                $categories = get_categories(array(
                    'orderby' => 'count',
                    'order' => 'DESC',
                    'number' => 5,
                    'hide_empty' => true,
                ));

                if ($categories) :
                    foreach ($categories as $category) :
                        $category_colors = array(
                            'selfhosting' => 'selfhosting',
                            'hacking' => 'hacking',
                            'cve' => 'cve',
                            'cvd' => 'cve',
                        );
                        $color_class = isset($category_colors[$category->slug]) ? $category_colors[$category->slug] : 'selfhosting';
                        $is_current = (is_category($category->term_id)) ? 'active' : '';
                ?>
                    <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="<?php echo esc_attr($is_current); ?>" aria-pressed="<?php echo $is_current ? 'true' : 'false'; ?>">
                        <span class="tag-color <?php echo esc_attr($color_class); ?>" aria-hidden="true"></span>
                        <?php echo esc_html($category->name); ?>
                    </a>
                <?php
                    endforeach;
                endif;
                ?>
            </div>
        </div>
        <footer class="sidebar-footer" aria-label="<?php esc_attr_e('Site footer links', 'upcode'); ?>">
            <a href="<?php echo esc_url(get_privacy_policy_url()); ?>"><?php esc_html_e('Privacy', 'upcode'); ?></a>
            <?php if (is_user_logged_in()) : ?>
                <a href="<?php echo esc_url(admin_url()); ?>"><?php esc_html_e('Admin', 'upcode'); ?></a>
            <?php endif; ?>
        </footer>
    </div>
</aside>